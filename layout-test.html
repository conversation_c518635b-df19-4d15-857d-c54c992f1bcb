<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA布局测试 - 严格约束验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .test-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .test-btn:hover {
            background: #007bff;
            color: white;
        }
        
        .test-btn.active {
            background: #007bff;
            color: white;
        }
        
        .iframe-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.5s ease;
            margin-bottom: 20px;
        }
        
        .test-iframe {
            width: 100%;
            height: 800px;
            border: none;
            display: block;
        }
        
        .size-info {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #ddd;
        }
        
        .layout-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .layout-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .layout-structure {
            font-family: 'Courier New', monospace;
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-line;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 OTA订单处理系统布局测试</h1>
            <p>验证严格布局约束在不同屏幕尺寸下的表现</p>
        </div>
        
        <div class="layout-info">
            <h3>📋 目标布局结构</h3>
            <div class="layout-structure">左列                    右列
┌─────────────┐    ┌─────────────┐
│  订单输入    │    │  基本信息    │
├─────────────┤    ├─────────────┤
│  行程信息    │    │  客户信息    │
├─────────────┤    ├─────────────┤
│  特殊需求    │    │  服务配置    │
└─────────────┘    ├─────────────┤
                   │  额外要求    │
                   └─────────────┘
┌─────────────────────────────────┐
│     数据异常提示  |  创建订单     │
└─────────────────────────────────┘</div>
        </div>
        
        <div class="test-controls">
            <button class="test-btn active" onclick="setSize('desktop')">🖥️ 桌面端 (1200px)</button>
            <button class="test-btn" onclick="setSize('tablet')">📱 平板端 (900px)</button>
            <button class="test-btn" onclick="setSize('mobile')">📱 移动端 (375px)</button>
            <button class="test-btn" onclick="setSize('custom')">⚙️ 自定义尺寸</button>
        </div>
        
        <div class="iframe-container" id="iframeContainer">
            <iframe src="index.html" class="test-iframe" id="testIframe"></iframe>
            <div class="size-info" id="sizeInfo">当前尺寸: 1200px × 800px (桌面端)</div>
        </div>
        
        <div class="layout-info">
            <h3>✅ 验证要点</h3>
            <ul>
                <li><strong>桌面端 (>1024px)</strong>: 左右两列布局，左3右4板块，操作按钮跨列显示</li>
                <li><strong>平板端 (768-1024px)</strong>: 保持两列布局，适当调整间距和字体</li>
                <li><strong>移动端 (<768px)</strong>: 保持两列布局，优化触摸交互</li>
                <li><strong>所有尺寸</strong>: 右列内容完全可见，无截断或溢出</li>
                <li><strong>操作按钮</strong>: 始终位于底部，跨越两列显示</li>
            </ul>
        </div>
    </div>

    <script>
        function setSize(type) {
            const container = document.getElementById('iframeContainer');
            const iframe = document.getElementById('testIframe');
            const sizeInfo = document.getElementById('sizeInfo');
            const buttons = document.querySelectorAll('.test-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            let width, height, description;
            
            switch(type) {
                case 'desktop':
                    width = '1200px';
                    height = '800px';
                    description = '桌面端';
                    break;
                case 'tablet':
                    width = '900px';
                    height = '700px';
                    description = '平板端';
                    break;
                case 'mobile':
                    width = '375px';
                    height = '600px';
                    description = '移动端';
                    break;
                case 'custom':
                    const customWidth = prompt('请输入宽度 (px):', '1024');
                    const customHeight = prompt('请输入高度 (px):', '768');
                    if (customWidth && customHeight) {
                        width = customWidth + 'px';
                        height = customHeight + 'px';
                        description = '自定义';
                    } else {
                        return;
                    }
                    break;
            }
            
            container.style.width = width;
            container.style.margin = '0 auto';
            iframe.style.height = height;
            sizeInfo.textContent = `当前尺寸: ${width} × ${height} (${description})`;
        }
        
        // 初始化
        setSize('desktop');
    </script>
</body>
</html>
