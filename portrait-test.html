<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA竖屏布局测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 10px;
            background: #f0f0f0;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .test-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 8px 16px;
            border: 1px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 12px;
        }
        
        .test-btn:hover {
            background: #007bff;
            color: white;
        }
        
        .test-btn.active {
            background: #007bff;
            color: white;
        }
        
        .iframe-container {
            border: 1px solid #ddd;
            border-radius: 6px;
            overflow: hidden;
            transition: all 0.5s ease;
            margin-bottom: 15px;
        }
        
        .test-iframe {
            width: 100%;
            border: none;
            display: block;
        }
        
        .size-info {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #ddd;
        }
        
        .checklist {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        
        .checklist h3 {
            margin: 0 0 10px 0;
            color: #2e7d32;
            font-size: 14px;
        }
        
        .checklist ul {
            margin: 0;
            padding-left: 20px;
            font-size: 12px;
        }
        
        .checklist li {
            margin-bottom: 5px;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📱 竖屏布局测试</h1>
            <p>验证竖屏显示时的内容可见性</p>
        </div>
        
        <div class="warning">
            <strong>⚠️ 测试重点</strong><br>
            确保在竖屏模式下：服务配置、额外要求、底部操作区域都完全可见
        </div>
        
        <div class="checklist">
            <h3>✅ 竖屏验证清单</h3>
            <ul>
                <li>服务配置板块内容完全可见</li>
                <li>额外要求板块完全可见</li>
                <li>底部操作区域（数据异常提示 + 创建订单）可见</li>
                <li>所有板块内容可以正常滚动</li>
                <li>布局保持左右两列结构</li>
            </ul>
        </div>
        
        <div class="test-controls">
            <button class="test-btn active" onclick="setSize('portrait-phone')">📱 竖屏手机</button>
            <button class="test-btn" onclick="setSize('portrait-tablet')">📱 竖屏平板</button>
            <button class="test-btn" onclick="setSize('square')">⬜ 正方形</button>
        </div>
        
        <div class="iframe-container" id="iframeContainer">
            <iframe src="index.html" class="test-iframe" id="testIframe"></iframe>
            <div class="size-info" id="sizeInfo">当前尺寸: 375px × 667px (竖屏手机)</div>
        </div>
        
        <div class="checklist">
            <h3>🔧 修复内容</h3>
            <ul>
                <li><strong>移除最大高度限制</strong> - 允许内容完全展开</li>
                <li><strong>添加竖屏专用CSS规则</strong> - @media (orientation: portrait)</li>
                <li><strong>优化Grid行分配</strong> - 使用 minmax(0, 1fr) auto</li>
                <li><strong>减少竖屏时的最小高度</strong> - 确保所有板块可见</li>
                <li><strong>操作按钮粘性定位</strong> - position: sticky 确保始终可见</li>
            </ul>
        </div>
    </div>

    <script>
        function setSize(type) {
            const container = document.getElementById('iframeContainer');
            const iframe = document.getElementById('testIframe');
            const sizeInfo = document.getElementById('sizeInfo');
            const buttons = document.querySelectorAll('.test-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            let width, height, description;
            
            switch(type) {
                case 'portrait-phone':
                    width = '375px';
                    height = '667px';
                    description = '竖屏手机';
                    break;
                case 'portrait-tablet':
                    width = '768px';
                    height = '1024px';
                    description = '竖屏平板';
                    break;
                case 'square':
                    width = '500px';
                    height = '500px';
                    description = '正方形';
                    break;
            }
            
            container.style.width = width;
            container.style.margin = '0 auto';
            iframe.style.height = height;
            sizeInfo.textContent = `当前尺寸: ${width} × ${height} (${description})`;
        }
        
        // 初始化
        setSize('portrait-phone');
    </script>
</body>
</html>
