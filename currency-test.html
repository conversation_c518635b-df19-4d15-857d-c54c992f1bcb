<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货币转换功能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: var(--bg-tertiary);
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--neu-shadow-outset);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .test-section h3 {
            color: var(--color-primary);
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: var(--radius-md);
            background: var(--color-primary);
            color: white;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all var(--transition-normal);
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(247, 92, 244, 0.3);
        }
        
        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .conversion-info {
            background: var(--bg-primary);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .api-info {
            background: var(--color-gray-100);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .rate-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .rate-item {
            background: var(--bg-primary);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .rate-item h4 {
            margin: 0 0 10px 0;
            color: var(--color-primary);
        }
        
        .rate-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-primary);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>💱 货币转换功能测试</h1>
        <p>测试系统中的货币转换和计算功能</p>
        
        <div class="test-section">
            <h3>🧮 货币转换器功能</h3>
            <div class="form-group">
                <label for="testAmount">输入金额</label>
                <div class="price-input-group">
                    <input type="number" id="testAmount" step="0.01" min="0" placeholder="输入金额">
                    <select id="testFromCurrency">
                        <option value="MYR">MYR</option>
                        <option value="USD">USD</option>
                        <option value="CNY">CNY</option>
                    </select>
                </div>
            </div>
            
            <div>
                <button class="btn" onclick="testConversion()">转换到MYR</button>
                <button class="btn btn-secondary" onclick="showAllRates()">显示所有汇率</button>
                <button class="btn btn-secondary" onclick="parseTextPrice()">文本价格识别</button>
                <button class="btn btn-secondary" onclick="testBatchConversion()">批量转换测试</button>
            </div>
            
            <div id="conversionResult" class="conversion-info" style="display: none;">
                <h4>转换结果：</h4>
                <div id="resultContent"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 价格文本识别测试</h3>
            <div class="form-group">
                <label for="textInput">输入包含价格的文本</label>
                <textarea id="textInput" rows="3" style="width: 100%; padding: 10px; border: 1px solid var(--border-color); border-radius: 4px;" 
                    placeholder="例如: 价格是RM150.00 或者 $50.50 或者 ￥300元">价格是RM150.00，美金是$45.20，人民币￥280元</textarea>
            </div>
            <button class="btn" onclick="parseTextPrices()">识别所有价格</button>
            
            <div id="parseResult" class="conversion-info" style="display: none;">
                <h4>识别结果：</h4>
                <div id="parseContent"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>⚙️ 汇率管理测试</h3>
            <div class="rate-grid" id="rateGrid">
                <!-- 汇率信息将动态填充 -->
            </div>
            
            <div style="margin-top: 20px;">
                <button class="btn" onclick="updateRate()">更新汇率</button>
                <button class="btn btn-secondary" onclick="resetRates()">重置默认汇率</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 系统集成测试</h3>
            <p>测试价格输入组件的实时转换功能（需要主系统支持）</p>
            
            <div class="form-group">
                <label for="systemPrice">系统价格输入</label>
                <div class="price-input-group" id="systemPriceGroup">
                    <input type="number" id="systemPrice" step="0.01" min="0" placeholder="输入价格测试实时转换">
                    <select id="systemCurrency">
                        <option value="MYR">MYR</option>
                        <option value="USD">USD</option>
                        <option value="CNY">CNY</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 API 使用说明</h3>
            <div class="api-info">
                <h4>主要功能：</h4>
                <ul>
                    <li><strong>getCurrencyConverter()</strong> - 获取货币转换器实例</li>
                    <li><strong>convertToMYR(amount, currency)</strong> - 转换到马来币</li>
                    <li><strong>parsePrice(text)</strong> - 从文本识别价格</li>
                    <li><strong>processOrderPrice(orderData)</strong> - 处理订单价格</li>
                    <li><strong>updateExchangeRate(currency, rate)</strong> - 更新汇率</li>
                </ul>
                
                <h4>支持的货币：</h4>
                <ul>
                    <li><strong>MYR</strong> - 马来西亚令吉（基础货币）</li>
                    <li><strong>USD</strong> - 美元</li>
                    <li><strong>CNY</strong> - 人民币</li>
                </ul>
                
                <h4>默认汇率：</h4>
                <ul>
                    <li>1 CNY = 0.615 MYR</li>
                    <li>1 USD = 4.4 MYR</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript -->
    <script src="js/logger.js"></script>
    <script src="js/currency-converter.js"></script>
    
    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            showAllRates();
            
            // 绑定实时转换（模拟主系统功能）
            const systemPrice = document.getElementById('systemPrice');
            const systemCurrency = document.getElementById('systemCurrency');
            
            if (systemPrice && systemCurrency) {
                systemPrice.addEventListener('input', updateSystemConversion);
                systemCurrency.addEventListener('change', updateSystemConversion);
            }
        });
        
        // 转换测试
        function testConversion() {
            const amount = parseFloat(document.getElementById('testAmount').value);
            const fromCurrency = document.getElementById('testFromCurrency').value;
            
            if (!amount || amount <= 0) {
                alert('请输入有效金额');
                return;
            }
            
            const converter = getCurrencyConverter();
            const result = converter.convertToMYR(amount, fromCurrency);
            
            showResult('转换结果', result);
        }
        
        // 显示所有汇率
        function showAllRates() {
            const converter = getCurrencyConverter();
            const rates = converter.getExchangeRates();
            const currencies = converter.getSupportedCurrencies();
            
            const rateGrid = document.getElementById('rateGrid');
            rateGrid.innerHTML = '';
            
            currencies.forEach(currency => {
                const rateItem = document.createElement('div');
                rateItem.className = 'rate-item';
                rateItem.innerHTML = `
                    <h4>${currency.code}</h4>
                    <div class="rate-value">${currency.rate}</div>
                    <small>${currency.name}</small>
                `;
                rateGrid.appendChild(rateItem);
            });
        }
        
        // 价格文本识别
        function parseTextPrice() {
            const text = "价格RM120.50";
            const converter = getCurrencyConverter();
            const result = converter.parsePrice(text);
            
            showResult('文本识别结果', result);
        }
        
        // 解析所有价格
        function parseTextPrices() {
            const text = document.getElementById('textInput').value;
            if (!text.trim()) {
                alert('请输入包含价格的文本');
                return;
            }
            
            const converter = getCurrencyConverter();
            
            // 分段解析
            const pricePatterns = [
                /(?:RM|MYR)\s*(\d+(?:\.\d{2})?)/gi,
                /(?:\$|USD)\s*(\d+(?:\.\d{2})?)/gi,
                /(?:￥|CNY|RMB)\s*(\d+(?:\.\d{2})?)/gi
            ];
            
            const results = [];
            pricePatterns.forEach(pattern => {
                let match;
                while ((match = pattern.exec(text)) !== null) {
                    const result = converter.parsePrice(match[0]);
                    if (result) {
                        const conversion = converter.convertToMYR(result.originalAmount, result.originalCurrency);
                        results.push({
                            original: result,
                            conversion: conversion
                        });
                    }
                }
            });
            
            const parseResult = document.getElementById('parseResult');
            const parseContent = document.getElementById('parseContent');
            
            if (results.length > 0) {
                parseContent.innerHTML = results.map((result, index) => `
                    <div style="margin-bottom: 10px; padding: 10px; background: var(--bg-tertiary); border-radius: 4px;">
                        <strong>识别 ${index + 1}:</strong> ${result.original.originalText}<br>
                        <strong>转换:</strong> ${result.conversion.originalAmount} ${result.conversion.originalCurrency} → ${result.conversion.convertedAmount} MYR
                        ${result.conversion.needsConversion ? ` (汇率: ${result.conversion.exchangeRate})` : ''}
                    </div>
                `).join('');
            } else {
                parseContent.innerHTML = '<p>未识别到有效价格</p>';
            }
            
            parseResult.style.display = 'block';
        }
        
        // 批量转换测试
        function testBatchConversion() {
            const testOrders = [
                { price: 100, currency: 'USD' },
                { price: 200, currency: 'CNY' },
                { price: 150, currency: 'MYR' }
            ];
            
            const converter = getCurrencyConverter();
            const results = testOrders.map(order => 
                converter.convertToMYR(order.price, order.currency)
            );
            
            showResult('批量转换结果', results);
        }
        
        // 更新汇率
        function updateRate() {
            const currency = prompt('输入货币代码 (USD/CNY):');
            const rate = parseFloat(prompt('输入新汇率:'));
            
            if (currency && rate && rate > 0) {
                try {
                    const converter = getCurrencyConverter();
                    converter.updateExchangeRate(currency, rate);
                    showAllRates();
                    alert(`${currency} 汇率已更新为 ${rate}`);
                } catch (error) {
                    alert(`更新失败: ${error.message}`);
                }
            }
        }
        
        // 重置汇率
        function resetRates() {
            if (confirm('确定要重置所有汇率为默认值吗？')) {
                const converter = getCurrencyConverter();
                converter.resetToDefaultRates();
                showAllRates();
                alert('汇率已重置为默认值');
            }
        }
        
        // 系统转换更新（模拟主系统功能）
        function updateSystemConversion() {
            const amount = parseFloat(document.getElementById('systemPrice').value);
            const currency = document.getElementById('systemCurrency').value;
            const priceGroup = document.getElementById('systemPriceGroup');
            
            // 清除状态
            priceGroup.classList.remove('valid', 'invalid');
            
            // 移除现有转换显示
            const existingDisplay = priceGroup.parentNode.querySelector('.price-conversion-display');
            if (existingDisplay) {
                existingDisplay.remove();
            }
            
            if (!amount || amount <= 0) {
                return;
            }
            
            try {
                const converter = getCurrencyConverter();
                const result = converter.convertToMYR(amount, currency);
                
                if (result.needsConversion) {
                    // 创建转换显示
                    const conversionDisplay = document.createElement('div');
                    conversionDisplay.className = 'price-conversion-display';
                    conversionDisplay.innerHTML = `
                        <span class="conversion-icon">💱</span>
                        <span class="conversion-text">约等于</span>
                        <span class="conversion-rate">${converter.formatPrice(result.convertedAmount, 'MYR')}</span>
                    `;
                    
                    priceGroup.parentNode.appendChild(conversionDisplay);
                    priceGroup.classList.add('valid');
                } else {
                    priceGroup.classList.add('valid');
                }
            } catch (error) {
                priceGroup.classList.add('invalid');
            }
        }
        
        // 显示结果
        function showResult(title, data) {
            const resultDiv = document.getElementById('conversionResult');
            const contentDiv = document.getElementById('resultContent');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = `
                <h4>${title}</h4>
                <pre style="background: var(--bg-primary); padding: 10px; border-radius: 4px; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>
            `;
        }
    </script>
</body>
</html>
