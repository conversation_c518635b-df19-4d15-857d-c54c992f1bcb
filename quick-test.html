<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速竖屏测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .test-frame {
            width: 375px;
            height: 667px;
            margin: 0 auto;
            border: 2px solid #333;
            border-radius: 20px;
            overflow: hidden;
            background: white;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        }
        
        .test-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .info {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .checklist {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .checklist h3 {
            margin: 0 0 10px 0;
            color: #2e7d32;
        }
        
        .checklist ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .checklist li {
            margin-bottom: 8px;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>📱 竖屏布局修复验证</h1>
        <p>模拟iPhone 8 (375×667) 竖屏显示</p>
    </div>
    
    <div class="warning">
        <strong>⚠️ 修复前的问题</strong><br>
        • 服务配置显示被切断<br>
        • 额外要求板块不可见<br>
        • 底部操作区域完全不可见
    </div>
    
    <div class="success">
        <strong>✅ 修复内容</strong><br>
        • 移除最大高度限制 (max-height: none)<br>
        • 添加竖屏专用CSS规则<br>
        • 优化Grid行分配 (minmax(0, 1fr) auto)<br>
        • 允许body和容器滚动<br>
        • 减少竖屏时的最小高度约束
    </div>
    
    <div class="test-frame">
        <iframe src="index.html" class="test-iframe"></iframe>
    </div>
    
    <div class="checklist">
        <h3>✅ 验证清单</h3>
        <ul>
            <li><strong>服务配置板块</strong> - 乘客人数、行李件数、行驶区域、语言要求、价格字段都应该可见</li>
            <li><strong>额外要求板块</strong> - 文本区域应该完全可见</li>
            <li><strong>底部操作区域</strong> - "数据异常提示"和"创建订单"按钮应该可见</li>
            <li><strong>滚动功能</strong> - 页面应该可以正常滚动查看所有内容</li>
            <li><strong>布局结构</strong> - 保持左右两列布局不变</li>
        </ul>
    </div>
    
    <div class="info">
        <p><strong>测试方法</strong>：在上方的模拟器中滚动页面，确认所有板块和按钮都能正常显示和访问。</p>
    </div>
</body>
</html>
