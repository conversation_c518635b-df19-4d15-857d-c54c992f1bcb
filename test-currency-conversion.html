<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货币转换功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .price-input-group {
            display: flex;
            gap: 10px;
        }
        .price-input-group input {
            flex: 2;
        }
        .price-input-group select {
            flex: 1;
        }
        .conversion-display {
            margin-top: 10px;
            padding: 10px;
            background: #f0f8ff;
            border: 1px solid #007acc;
            border-radius: 4px;
            font-weight: bold;
            color: #007acc;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #005a99;
        }
    </style>
</head>
<body>
    <h1>🧪 货币转换功能测试</h1>
    
    <div class="test-container">
        <h2>测试新的价格显示逻辑</h2>
        <p>测试要求：</p>
        <ul>
            <li>✅ SGD支持（汇率3.4）</li>
            <li>✅ USD汇率调整（4.3）</li>
            <li>✅ 新显示格式：原价 → 转换后</li>
            <li>✅ MYR订单隐藏转换显示</li>
        </ul>
        
        <div class="form-group">
            <label for="testPrice">测试价格：</label>
            <div class="price-input-group">
                <input type="number" id="testPrice" value="100" step="0.01" min="0">
                <select id="testCurrency">
                    <option value="MYR">MYR</option>
                    <option value="USD">USD</option>
                    <option value="SGD">SGD</option>
                    <option value="CNY">CNY</option>
                </select>
            </div>
        </div>
        
        <div id="conversionResult" class="conversion-display" style="display: none;"></div>
        
        <button onclick="testConversion()">测试转换</button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>预期结果验证</h2>
        <div id="expectedResults">
            <p><strong>100 USD → 430 MYR</strong> (汇率4.3)</p>
            <p><strong>100 SGD → 340 MYR</strong> (汇率3.4)</p>
            <p><strong>100 CNY → 61.5 MYR</strong> (汇率0.615)</p>
            <p><strong>100 MYR → 隐藏转换显示</strong></p>
        </div>
    </div>

    <script src="js/currency-converter.js"></script>
    <script>
        // 测试函数
        function testConversion() {
            const price = parseFloat(document.getElementById('testPrice').value);
            const currency = document.getElementById('testCurrency').value;
            const resultDiv = document.getElementById('conversionResult');
            const testResultsDiv = document.getElementById('testResults');
            
            if (!price || price <= 0) {
                showResult('请输入有效的价格', 'error');
                return;
            }
            
            try {
                // 获取货币转换器实例
                const converter = new CurrencyConverter();
                
                // 执行转换
                const result = converter.convertToMYR(price, currency);
                
                // 显示结果
                if (result.needsConversion) {
                    const originalPrice = converter.formatPrice(price, currency);
                    const convertedPrice = converter.formatPrice(result.convertedAmount, 'MYR');
                    
                    resultDiv.innerHTML = `原价 ${originalPrice} → 转换后 ${convertedPrice}`;
                    resultDiv.style.display = 'block';
                    
                    showResult(`✅ 转换成功：${originalPrice} → ${convertedPrice}`, 'success');
                } else {
                    resultDiv.style.display = 'none';
                    showResult(`✅ MYR订单，正确隐藏转换显示`, 'success');
                }
                
                // 验证汇率
                validateExchangeRate(currency, result.exchangeRate);
                
            } catch (error) {
                showResult(`❌ 转换失败：${error.message}`, 'error');
                resultDiv.style.display = 'none';
            }
        }
        
        function validateExchangeRate(currency, actualRate) {
            const expectedRates = {
                'USD': 4.3,
                'SGD': 3.4,
                'CNY': 0.615,
                'MYR': 1.0
            };
            
            const expected = expectedRates[currency];
            if (expected && Math.abs(actualRate - expected) < 0.001) {
                showResult(`✅ 汇率验证通过：${currency} = ${actualRate}`, 'success');
            } else if (expected) {
                showResult(`❌ 汇率验证失败：${currency} 期望 ${expected}，实际 ${actualRate}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const testResultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `test-result ${type}`;
            resultElement.textContent = message;
            testResultsDiv.appendChild(resultElement);
            
            // 自动滚动到结果
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', function() {
            // 测试所有货币
            const currencies = ['USD', 'SGD', 'CNY', 'MYR'];
            currencies.forEach((currency, index) => {
                setTimeout(() => {
                    document.getElementById('testCurrency').value = currency;
                    testConversion();
                }, index * 1000);
            });
        });
    </script>
</body>
</html>
