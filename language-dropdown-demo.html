<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言下拉菜单演示</title>
    <style>
        :root {
            --color-primary: #F75CF4;
            --bg-primary: #1a1a2e;
            --bg-secondary: #16213e;
            --bg-tertiary: #0f3460;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #333;
            --spacing-1: 4px;
            --spacing-2: 8px;
            --spacing-3: 12px;
            --radius-md: 8px;
            --font-size-base: 14px;
            --font-size-sm: 12px;
            --transition-normal: 0.3s ease;
            --transition-fast: 0.15s ease;
            --neu-shadow-inset: inset 2px 2px 5px rgba(0,0,0,0.3), inset -2px -2px 5px rgba(255,255,255,0.05);
            --neu-shadow-outset: 2px 2px 5px rgba(0,0,0,0.3), -2px -2px 5px rgba(255,255,255,0.05);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 600px;
            margin: 0 auto;
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 30px;
            box-shadow: var(--neu-shadow-outset);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
        }
        
        .demo-header h1 {
            color: var(--color-primary);
            margin: 0 0 10px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .demo-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: var(--font-size-base);
            transition: all var(--transition-normal);
            background: var(--color-primary);
            color: white;
            box-shadow: var(--neu-shadow-outset);
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--neu-shadow-outset), 0 4px 12px rgba(247, 92, 244, 0.3);
        }
        
        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }
        
        .result-display {
            background: var(--bg-tertiary);
            padding: 15px;
            border-radius: var(--radius-md);
            margin-top: 20px;
            box-shadow: var(--neu-shadow-inset);
        }
        
        .result-display h3 {
            margin: 0 0 10px 0;
            color: var(--color-primary);
        }
        
        .result-display pre {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            color: #00ff88;
        }
    </style>
    
    <!-- 引入必要的CSS -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🔽 语言多选下拉菜单演示</h1>
            <p>测试新的多选下拉菜单组件功能</p>
        </div>
        
        <div class="form-group">
            <label for="languagesIdArray">语言要求</label>
            <div class="multi-select-dropdown" id="languagesDropdown">
                <div class="multi-select-trigger" id="languagesTrigger" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox" title="选择语言要求">
                    <span class="multi-select-text">请选择语言</span>
                    <span class="multi-select-arrow">▼</span>
                </div>
                <div class="multi-select-options" id="languagesOptions" role="listbox" aria-label="语言选项">
                    <!-- 选项将通过JavaScript动态生成 -->
                </div>
                <!-- 隐藏的原始select元素，保持表单兼容性 -->
                <select id="languagesIdArray" multiple title="选择语言要求">
                    <option value="">请选择语言</option>
                    <option value="1">中文</option>
                    <option value="2">English</option>
                    <option value="3">Bahasa Malaysia</option>
                    <option value="4">ภาษาไทย</option>
                    <option value="5">한국어</option>
                    <option value="6">日本語</option>
                    <option value="7">Français</option>
                    <option value="8">Deutsch</option>
                </select>
            </div>
        </div>
        
        <div class="demo-actions">
            <button class="btn" onclick="getSelectedValues()">获取选中值</button>
            <button class="btn btn-secondary" onclick="setTestValues()">设置测试值</button>
            <button class="btn btn-secondary" onclick="clearSelection()">清空选择</button>
            <button class="btn btn-secondary" onclick="toggleDropdown()">切换下拉状态</button>
        </div>
        
        <div class="result-display">
            <h3>📊 当前状态</h3>
            <div id="resultContent">
                <p>请点击上方按钮测试功能...</p>
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript -->
    <script src="js/multi-select-dropdown.js"></script>
    
    <script>
        // 全局变量
        let dropdown;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 创建多选下拉菜单实例
            dropdown = new MultiSelectDropdown('languagesDropdown', {
                placeholder: '请选择语言',
                maxDisplayItems: 3
            });
            
            // 监听变化事件
            const hiddenSelect = document.getElementById('languagesIdArray');
            hiddenSelect.addEventListener('change', function() {
                updateResult('选择已更改');
            });
            
            console.log('多选下拉菜单演示已初始化');
        });
        
        // 获取选中值
        function getSelectedValues() {
            const values = dropdown.getSelectedValues();
            const hiddenSelect = document.getElementById('languagesIdArray');
            const selectedOptions = Array.from(hiddenSelect.selectedOptions);
            
            updateResult('获取选中值', {
                '组件返回值': values,
                '隐藏select选中值': selectedOptions.map(opt => ({
                    value: opt.value,
                    text: opt.textContent
                })),
                '选中数量': values.length
            });
        }
        
        // 设置测试值
        function setTestValues() {
            const testValues = ['2', '3', '5']; // English, Bahasa Malaysia, 한국어
            dropdown.setSelectedValues(testValues);
            
            updateResult('设置测试值', {
                '设置的值': testValues,
                '对应语言': ['English', 'Bahasa Malaysia', '한국어']
            });
        }
        
        // 清空选择
        function clearSelection() {
            dropdown.clearSelection();
            updateResult('已清空选择');
        }
        
        // 切换下拉状态
        function toggleDropdown() {
            dropdown.toggle();
            updateResult('切换下拉状态', {
                '当前状态': dropdown.isOpen ? '展开' : '收起'
            });
        }
        
        // 更新结果显示
        function updateResult(action, data = null) {
            const resultContent = document.getElementById('resultContent');
            const timestamp = new Date().toLocaleTimeString();
            
            let html = `<p><strong>操作:</strong> ${action}</p>`;
            html += `<p><strong>时间:</strong> ${timestamp}</p>`;
            
            if (data) {
                html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            }
            
            resultContent.innerHTML = html;
        }
    </script>
</body>
</html>
