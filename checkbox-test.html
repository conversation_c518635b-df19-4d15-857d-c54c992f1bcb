<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多选下拉菜单勾选功能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: var(--bg-tertiary);
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--neu-shadow-outset);
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-section h3 {
            color: var(--color-primary);
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .result {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 3px solid var(--color-primary);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: var(--radius-md);
            background: var(--color-primary);
            color: white;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all var(--transition-normal);
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(247, 92, 244, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 多选下拉菜单勾选功能测试</h1>
        
        <div class="test-section">
            <h3>语言选择测试</h3>
            <div class="form-group">
                <label for="languagesTest">选择语言要求</label>
                <div class="multi-select-dropdown" id="languagesDropdownTest">
                    <div class="multi-select-trigger" id="languagesTriggerTest" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox">
                        <span class="multi-select-text">请选择语言</span>
                        <span class="multi-select-arrow">▼</span>
                    </div>
                    <div class="multi-select-options" id="languagesOptionsTest" role="listbox">
                        <!-- 选项将通过JavaScript动态生成 -->
                    </div>
                    <select id="languagesTest" multiple>
                        <option value="">请选择语言</option>
                        <option value="1">中文</option>
                        <option value="2">English</option>
                        <option value="3">Bahasa Malaysia</option>
                        <option value="4">ภาษาไทย</option>
                        <option value="5">한국어</option>
                        <option value="6">日本語</option>
                        <option value="7">Français</option>
                        <option value="8">Deutsch</option>
                        <option value="9">Español</option>
                        <option value="10">Русский</option>
                    </select>
                </div>
            </div>
            
            <div>
                <button class="btn" onclick="testGetValues()">获取选中值</button>
                <button class="btn" onclick="testSetValues()">设置测试值</button>
                <button class="btn" onclick="testClear()">清空选择</button>
                <button class="btn" onclick="testSelectAll()">全选</button>
            </div>
            
            <div id="testResult" class="result" style="display: none;">
                <h4>测试结果：</h4>
                <div id="resultContent"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>勾选框样式对比</h3>
            <p>检查勾选框的视觉效果和交互状态（应该是正方形）：</p>
            
            <div style="padding: 20px; background: var(--bg-secondary); border-radius: 8px;">
                <div style="margin-bottom: 15px; display: flex; align-items: center;">
                    <input type="checkbox" class="multi-select-checkbox" id="test1">
                    <label for="test1" class="multi-select-label">未选中状态 - 应该是16x16px正方形</label>
                </div>
                <div style="margin-bottom: 15px; display: flex; align-items: center;">
                    <input type="checkbox" class="multi-select-checkbox" id="test2" checked>
                    <label for="test2" class="multi-select-label">已选中状态 - 紫色背景，白色对勾</label>
                </div>
                <div style="margin-bottom: 15px; display: flex; align-items: center;">
                    <input type="checkbox" class="multi-select-checkbox" id="test3" disabled>
                    <label for="test3" class="multi-select-label">禁用状态</label>
                </div>
                <div style="margin-bottom: 15px; display: flex; align-items: center;">
                    <span style="width: 16px; height: 16px; background: red; margin-right: 8px; flex-shrink: 0;"></span>
                    <span>参考正方形（16x16px红色块）</span>
                </div>
            </div>
        </div>
    </div>

    <script src="js/multi-select-dropdown.js"></script>
    <script>
        let testDropdown;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化测试下拉菜单
            testDropdown = new MultiSelectDropdown('languagesDropdownTest', {
                placeholder: '请选择语言',
                maxDisplayItems: 3
            });
            
            // 监听变化
            document.getElementById('languagesTest').addEventListener('change', function() {
                console.log('Selection changed:', testDropdown.getSelectedValues());
            });
        });
        
        function testGetValues() {
            const values = testDropdown.getSelectedValues();
            showResult('获取选中值', {
                '选中的值': values,
                '选中数量': values.length,
                '选中的语言': values.map(v => {
                    const option = document.querySelector(`#languagesTest option[value="${v}"]`);
                    return option ? option.textContent : v;
                })
            });
        }
        
        function testSetValues() {
            const testValues = ['2', '5', '7']; // English, 한국어, Français
            testDropdown.setSelectedValues(testValues);
            showResult('设置测试值', {
                '设置的值': testValues,
                '对应语言': ['English', '한국어', 'Français']
            });
        }
        
        function testClear() {
            testDropdown.clearSelection();
            showResult('清空选择', { '操作': '已清空所有选择' });
        }
        
        function testSelectAll() {
            const allValues = Array.from(document.querySelectorAll('#languagesTest option'))
                .filter(opt => opt.value !== '')
                .map(opt => opt.value);
            testDropdown.setSelectedValues(allValues);
            showResult('全选', {
                '选中数量': allValues.length,
                '全部语言': allValues
            });
        }
        
        function showResult(action, data) {
            const resultDiv = document.getElementById('testResult');
            const contentDiv = document.getElementById('resultContent');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = `
                <p><strong>操作：</strong>${action}</p>
                <p><strong>时间：</strong>${new Date().toLocaleTimeString()}</p>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }
    </script>
</body>
</html>
