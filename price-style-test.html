<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务配置价格样式重构测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-tertiary);
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--neu-shadow-outset);
        }
        
        .test-section {
            margin-bottom: 40px;
        }
        
        .test-section h3 {
            color: var(--color-primary);
            margin-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }
        
        .demo-card {
            background: var(--bg-secondary);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .demo-card h4 {
            margin: 0 0 15px 0;
            color: var(--color-primary);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: var(--radius-md);
            background: var(--color-primary);
            color: white;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all var(--transition-normal);
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(247, 92, 244, 0.3);
        }
        
        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .feature-list {
            background: var(--bg-primary);
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .feature-list li {
            margin-bottom: 8px;
            color: var(--text-secondary);
        }
        
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 服务配置价格样式重构测试</h1>
        <p>展示重构后的现代化价格输入组件</p>
        
        <div class="test-section">
            <h3>💰 重构后的价格输入组件</h3>
            
            <div class="demo-grid">
                <div class="demo-card">
                    <h4>基础状态</h4>
                    <div class="form-group">
                        <label for="price1">订单价格</label>
                        <div class="price-input-group">
                            <input type="number" id="price1" step="0.01" min="0" placeholder="输入价格">
                            <select>
                                <option value="MYR">MYR</option>
                                <option value="USD">USD</option>
                                <option value="CNY">CNY</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="demo-card">
                    <h4>验证成功状态</h4>
                    <div class="form-group">
                        <label for="price2">订单价格</label>
                        <div class="price-input-group valid">
                            <input type="number" id="price2" step="0.01" min="0" value="150.00">
                            <select>
                                <option value="MYR" selected>MYR</option>
                                <option value="USD">USD</option>
                                <option value="CNY">CNY</option>
                            </select>
                        </div>
                        <div class="price-conversion-display">
                            <span class="conversion-icon">💱</span>
                            <span class="conversion-text">约等于</span>
                            <span class="conversion-rate">$34.50 USD</span>
                        </div>
                    </div>
                </div>
                
                <div class="demo-card">
                    <h4>验证失败状态</h4>
                    <div class="form-group">
                        <label for="price3">订单价格</label>
                        <div class="price-input-group invalid">
                            <input type="number" id="price3" step="0.01" min="0" value="-10">
                            <select>
                                <option value="MYR" selected>MYR</option>
                                <option value="USD">USD</option>
                                <option value="CNY">CNY</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="demo-card">
                    <h4>焦点状态（点击输入框查看）</h4>
                    <div class="form-group">
                        <label for="price4">订单价格</label>
                        <div class="price-input-group">
                            <input type="number" id="price4" step="0.01" min="0" placeholder="点击查看焦点效果">
                            <select>
                                <option value="MYR">MYR</option>
                                <option value="USD">USD</option>
                                <option value="CNY">CNY</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 功能测试</h3>
            <div class="form-group">
                <label for="testPrice">测试价格输入</label>
                <div class="price-input-group" id="testPriceGroup">
                    <input type="number" id="testPrice" step="0.01" min="0" placeholder="输入价格进行测试">
                    <select id="testCurrency">
                        <option value="MYR">MYR</option>
                        <option value="USD">USD</option>
                        <option value="CNY">CNY</option>
                    </select>
                </div>
                <div id="conversionDisplay" class="price-conversion-display" style="display: none;">
                    <span class="conversion-icon">💱</span>
                    <span class="conversion-text">约等于</span>
                    <span class="conversion-rate" id="convertedAmount"></span>
                </div>
            </div>
            
            <div>
                <button class="btn" onclick="validatePrice()">验证价格</button>
                <button class="btn btn-secondary" onclick="clearPrice()">清空</button>
                <button class="btn btn-secondary" onclick="setRandomPrice()">随机价格</button>
                <button class="btn btn-secondary" onclick="toggleValidation()">切换验证状态</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>✨ 重构特性</h3>
            <div class="feature-list">
                <h4>🎨 视觉改进</h4>
                <ul>
                    <li>现代化的一体式设计，输入框和货币选择器无缝连接</li>
                    <li>增强的视觉反馈：悬停、焦点、验证状态</li>
                    <li>金钱图标 💰 提升用户体验</li>
                    <li>渐变背景的转换显示区域</li>
                </ul>
                
                <h4>🔧 功能增强</h4>
                <ul>
                    <li>智能验证状态显示（成功 ✓ / 错误 ⚠️）</li>
                    <li>实时货币转换显示</li>
                    <li>更大的点击区域，提升移动端体验</li>
                    <li>响应式设计，移动端自动纵向布局</li>
                </ul>
                
                <h4>⚡ 性能优化</h4>
                <ul>
                    <li>使用CSS transforms提升动画性能</li>
                    <li>优化的盒子模型和布局</li>
                    <li>减少重绘和重排</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 模拟汇率数据
        const exchangeRates = {
            'MYR': { 'USD': 0.23, 'CNY': 1.57 },
            'USD': { 'MYR': 4.35, 'CNY': 6.85 },
            'CNY': { 'MYR': 0.64, 'USD': 0.15 }
        };
        
        // 价格验证
        function validatePrice() {
            const priceInput = document.getElementById('testPrice');
            const priceGroup = document.getElementById('testPriceGroup');
            const value = parseFloat(priceInput.value);
            
            priceGroup.classList.remove('valid', 'invalid');
            
            if (value && value > 0) {
                priceGroup.classList.add('valid');
                showConversion();
            } else if (priceInput.value) {
                priceGroup.classList.add('invalid');
                hideConversion();
            } else {
                hideConversion();
            }
        }
        
        // 显示货币转换
        function showConversion() {
            const priceInput = document.getElementById('testPrice');
            const currencySelect = document.getElementById('testCurrency');
            const conversionDisplay = document.getElementById('conversionDisplay');
            const convertedAmount = document.getElementById('convertedAmount');
            
            const amount = parseFloat(priceInput.value);
            const fromCurrency = currencySelect.value;
            
            if (amount && fromCurrency) {
                const rates = exchangeRates[fromCurrency];
                if (rates) {
                    const toCurrency = fromCurrency === 'MYR' ? 'USD' : 'MYR';
                    const convertedValue = (amount * rates[toCurrency]).toFixed(2);
                    convertedAmount.textContent = `${convertedValue} ${toCurrency}`;
                    conversionDisplay.style.display = 'flex';
                }
            }
        }
        
        // 隐藏货币转换
        function hideConversion() {
            document.getElementById('conversionDisplay').style.display = 'none';
        }
        
        // 清空价格
        function clearPrice() {
            const priceInput = document.getElementById('testPrice');
            const priceGroup = document.getElementById('testPriceGroup');
            
            priceInput.value = '';
            priceGroup.classList.remove('valid', 'invalid');
            hideConversion();
        }
        
        // 设置随机价格
        function setRandomPrice() {
            const priceInput = document.getElementById('testPrice');
            const randomPrice = (Math.random() * 500 + 10).toFixed(2);
            priceInput.value = randomPrice;
            validatePrice();
        }
        
        let validationState = 0;
        function toggleValidation() {
            const priceGroup = document.getElementById('testPriceGroup');
            const priceInput = document.getElementById('testPrice');
            
            priceGroup.classList.remove('valid', 'invalid');
            
            if (validationState === 0) {
                priceInput.value = '150.00';
                priceGroup.classList.add('valid');
                showConversion();
                validationState = 1;
            } else if (validationState === 1) {
                priceInput.value = '-10';
                priceGroup.classList.add('invalid');
                hideConversion();
                validationState = 2;
            } else {
                priceInput.value = '';
                validationState = 0;
                hideConversion();
            }
        }
        
        // 实时验证
        document.getElementById('testPrice').addEventListener('input', validatePrice);
        document.getElementById('testCurrency').addEventListener('change', function() {
            if (document.getElementById('testPrice').value) {
                validatePrice();
            }
        });
    </script>
</body>
</html>
