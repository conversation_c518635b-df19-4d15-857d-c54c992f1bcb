/* CSS 自定义属性 - Neumorphism紫色主题设计 */
:root {
  /* 紫色主题配色方案 */
  --color-primary: #F75CF4;
  --color-primary-hover: #E040FB;
  --color-primary-light: #F8BBF5;
  --color-primary-gradient: linear-gradient(135deg, #F75CF4 0%, #E040FB 50%, #D500F9 100%);

  /* 辅助色 */
  --color-secondary: #8E8E93;
  --color-secondary-hover: #6B6B70;
  --color-secondary-light: #F0F0F3;

  /* 状态色 */
  --color-success: #4CAF50;
  --color-success-light: #E8F5E8;
  --color-warning: #FF9800;
  --color-warning-light: #FFF3E0;
  --color-error: #F44336;
  --color-error-light: #FFEBEE;
  --color-info: #2196F3;
  --color-info-light: #E3F2FD;

  /* Neumorphism背景色系 */
  --color-white: #FFFFFF;
  --color-neu-bg: #F0F0F3;        /* 主背景 */
  --color-neu-bg-secondary: #E6E6EA;  /* 次背景 */
  --color-neu-card: #FAFAFA;      /* 卡片背景 */
  --color-neu-light: #FFFFFF;     /* 高光色 */
  --color-neu-shadow: #D1D1D6;    /* 阴影色 */
  --color-neu-border: #E0E0E3;    /* 边框色 */

  /* 中性色 - 调整为适合Neumorphism */
  --color-gray-50: #F8F8FA;
  --color-gray-100: #F0F0F3;
  --color-gray-200: #E6E6EA;
  --color-gray-300: #D1D1D6;
  --color-gray-400: #A8A8B0;
  --color-gray-500: #8E8E93;
  --color-gray-600: #6B6B70;
  --color-gray-700: #48484A;
  --color-gray-800: #2D2D30;
  --color-gray-900: #1C1C1E;

  /* 背景色 */
  --bg-primary: var(--color-neu-bg);
  --bg-secondary: var(--color-neu-bg-secondary);
  --bg-tertiary: var(--color-neu-card);

  /* 文字色 */
  --text-primary: var(--color-gray-800);
  --text-secondary: var(--color-gray-600);
  --text-tertiary: var(--color-gray-500);
  --text-accent: var(--color-primary);

  /* 边框色 */
  --border-color: var(--color-neu-border);
  --border-hover: var(--color-gray-300);

  /* Neumorphism阴影效果 */
  --neu-shadow-outset: 8px 8px 16px var(--color-neu-shadow), -8px -8px 16px var(--color-neu-light);
  --neu-shadow-inset: inset 4px 4px 8px var(--color-neu-shadow), inset -4px -4px 8px var(--color-neu-light);
  --neu-shadow-pressed: inset 8px 8px 16px var(--color-neu-shadow), inset -8px -8px 16px var(--color-neu-light);
  --neu-shadow-hover: 12px 12px 24px var(--color-neu-shadow), -12px -12px 24px var(--color-neu-light);
  --neu-shadow-subtle: 4px 4px 8px var(--color-neu-shadow), -4px -4px 8px var(--color-neu-light);

  /* 传统阴影（备用） */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 保留毛玻璃效果（用于特殊场景） */
  --glass-bg: rgba(240, 240, 243, 0.8);
  --glass-bg-dark: rgba(0, 0, 0, 0.1);
  --glass-border: var(--color-neu-border);
  --glass-border-dark: rgba(255, 255, 255, 0.1);
  --glass-shadow: var(--neu-shadow-outset);
  --glass-blur: blur(16px);
  
  /* 紧凑化间距系统 - 大幅减少所有间距 */
  --spacing-1: 0.125rem;  /* 2px - 原0.25rem */
  --spacing-2: 0.25rem;   /* 4px - 原0.5rem */
  --spacing-3: 0.375rem;  /* 6px - 原0.75rem */
  --spacing-4: 0.5rem;    /* 8px - 原1rem */
  --spacing-5: 0.625rem;  /* 10px - 原1.25rem */
  --spacing-6: 0.75rem;   /* 12px - 原1.5rem */
  --spacing-8: 1rem;      /* 16px - 原2rem */
  --spacing-10: 1.25rem;  /* 20px - 原2.5rem */
  --spacing-12: 1.5rem;   /* 24px - 原3rem */
  
  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* 字体 */
  --font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* 行高 */
  --line-height-tight: 1.2;   /* 更紧凑的行高 */
  --line-height-normal: 1.4;  /* 减少行高 */
  --line-height-relaxed: 1.6; /* 减少行高 */

  /* 动态高度变量 */
  --header-height: 80px;
  --grid-height: calc(100vh - 200px);
  --grid-columns: 1fr 1fr;
  --grid-rows: 1fr 1fr;
}

/* 暗色主题 - 紫色Neumorphism协调配色 */
[data-theme="dark"] {
  /* 深紫色背景色系 */
  --color-neu-bg-dark: #1a0d1f;        /* 主背景 - 深紫黑 */
  --color-neu-bg-secondary-dark: #2d1b3d;  /* 次背景 - 深紫 */
  --color-neu-card-dark: #3d2a4a;      /* 卡片背景 - 中紫 */
  --color-neu-light-dark: #4a3357;     /* 高光色 - 亮紫 */
  --color-neu-shadow-dark: #0f0612;    /* 阴影色 - 极深紫 */
  --color-neu-border-dark: #5a4067;    /* 边框色 - 紫灰 */

  /* 应用暗色主题配色 */
  --bg-primary: var(--color-neu-bg-dark);
  --bg-secondary: var(--color-neu-bg-secondary-dark);
  --bg-tertiary: var(--color-neu-card-dark);

  /* 高对比度文字色 */
  --text-primary: #f0e6f7;    /* 浅紫白 - 主文字 */
  --text-secondary: #d1c4dd;  /* 紫灰 - 次要文字 */
  --text-tertiary: #a888b5;   /* 中紫灰 - 辅助文字 */
  --text-accent: #ff8cff;     /* 亮粉紫 - 强调色 */

  /* 边框色 */
  --border-color: var(--color-neu-border-dark);
  --border-hover: #6b4d78;

  /* 暗色主题专用Neumorphism阴影 */
  --neu-shadow-outset: 8px 8px 16px var(--color-neu-shadow-dark), -8px -8px 16px var(--color-neu-light-dark);
  --neu-shadow-inset: inset 4px 4px 8px var(--color-neu-shadow-dark), inset -4px -4px 8px var(--color-neu-light-dark);
  --neu-shadow-pressed: inset 8px 8px 16px var(--color-neu-shadow-dark), inset -8px -8px 16px var(--color-neu-light-dark);
  --neu-shadow-hover: 12px 12px 24px var(--color-neu-shadow-dark), -12px -12px 24px var(--color-neu-light-dark);
  --neu-shadow-subtle: 4px 4px 8px var(--color-neu-shadow-dark), -4px -4px 8px var(--color-neu-light-dark);

  /* 暗色毛玻璃效果 */
  --glass-bg: rgba(61, 42, 74, 0.8);
  --glass-bg-dark: rgba(26, 13, 31, 0.9);
  --glass-border: var(--color-neu-border-dark);
  --glass-border-dark: rgba(240, 230, 247, 0.1);
  --glass-shadow: var(--neu-shadow-outset);
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: var(--bg-primary);
  background-image: radial-gradient(circle at 20% 50%, rgba(247, 92, 244, 0.05) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(224, 64, 251, 0.05) 0%, transparent 50%),
                    radial-gradient(circle at 40% 80%, rgba(213, 0, 249, 0.05) 0%, transparent 50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden; /* 只隐藏水平滚动条 */
  overflow-y: auto; /* 允许垂直滚动 */
  min-height: 100vh; /* 最小高度，允许内容扩展 */
}

/* 布局容器 - 动态高度适配 */
#app {
  min-height: 100vh; /* 改为最小高度 */
  height: auto; /* 允许内容驱动 */
  display: flex;
  flex-direction: column;
  overflow: visible; /* 允许内容可见 */
}

.app-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-3) 0; /* 减少头部padding */
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: var(--font-size-xl);  /* 减少标题大小 */
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.title-icon {
  font-size: var(--font-size-2xl);  /* 减少图标大小 */
}

.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);  /* 减少头部控件间距 */
  flex-wrap: wrap;        /* 允许换行以适应小屏幕 */
  min-width: 0;          /* 允许收缩 */
}

/* 持久化邮箱输入 */
.persistent-email {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-right: var(--spacing-4);
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.persistent-email label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  white-space: nowrap;
}

.persistent-email input {
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  padding: var(--spacing-1);
  min-width: 200px;
  outline: none;
}

.persistent-email input:focus {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.persistent-email input.valid {
  color: var(--color-success);
}

.persistent-email input.invalid {
  color: var(--color-error);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);  /* 减少用户信息间距 */
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-3) var(--spacing-4);
  width: 100%;
  height: calc(100vh - var(--header-height, 80px));
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 登录面板 */
.login-panel {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.login-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-3); /* 进一步减少登录卡片padding */
  width: 100%;
  max-width: 400px;
  border: 1px solid var(--border-color);
}

.login-card h2 {
  text-align: center;
  margin-bottom: var(--spacing-2); /* 进一步减少标题下方间距 */
  color: var(--text-primary);
  font-size: var(--font-size-xl);
}

.login-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.login-actions .btn {
  width: 100%;
}

.login-actions .btn-sm {
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
}

/* 工作区 - 动态高度适配 */
.workspace {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  flex: 1;
  overflow: visible; /* 改为visible，允许内容完全展开 */
  height: auto; /* 改为auto，由内容驱动高度 */
  min-height: 100%; /* 确保至少占满容器 */
}

/* 严格布局约束的左右列布局容器 - 修复竖屏显示问题 */
.grid-container-new {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 左右两列严格等宽 */
  grid-template-rows: minmax(0, 1fr) auto; /* 主内容区自适应 + 操作按钮区固定 */
  gap: var(--spacing-2);
  min-height: calc(100vh - 200px); /* 最小高度保证 */
  height: auto; /* 允许内容驱动 */
  max-height: none; /* 移除最大高度限制，允许完全展开 */
  margin-bottom: var(--spacing-2);
  padding: var(--spacing-1);
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  position: relative;
  overflow: visible;
}

/* 左列容器 - 严格4板块布局 */
.column-left {
  display: grid;
  grid-template-rows:
    minmax(200px, auto)  /* 订单输入 - 最小200px */
    minmax(180px, auto)  /* 行程信息 - 最小180px */
    minmax(120px, auto)  /* 特殊需求 - 最小120px */
    minmax(100px, auto); /* 额外要求 - 最小100px */
  gap: 0;
  min-height: 0;
  align-content: start;
}

/* 右列容器 - 严格3板块布局 */
.column-right {
  display: grid;
  grid-template-rows:
    minmax(160px, auto)  /* 基本信息 - 最小160px */
    minmax(180px, auto)  /* 客户信息 - 最小180px */
    minmax(220px, auto); /* 服务配置 - 最小220px */
  gap: 0;
  min-height: 0;
  align-content: start;
}

/* 操作按钮区域跨越两列 */
.grid-span-full {
  grid-column: 1 / -1; /* 跨越所有列 */
  grid-row: 2; /* 位于第二行 */
}

/* 多选下拉菜单组件 - Fluent Design风格 */
.multi-select-dropdown {
  position: relative;
  width: 100%;
}

.multi-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  min-height: 44px; /* 确保触摸友好 */
  box-shadow: var(--neu-shadow-inset);
  user-select: none;
  -webkit-user-select: none;
}

.multi-select-trigger:hover {
  border-color: var(--color-primary);
  box-shadow: var(--neu-shadow-inset), 0 0 0 1px var(--color-primary);
}

.multi-select-trigger:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--neu-shadow-inset), 0 0 0 2px rgba(247, 92, 244, 0.3);
}

.multi-select-trigger[aria-expanded="true"] {
  border-color: var(--color-primary);
  box-shadow: var(--neu-shadow-inset), 0 0 0 1px var(--color-primary);
}

.multi-select-trigger[aria-expanded="true"] .multi-select-arrow {
  transform: rotate(180deg);
}

.multi-select-text {
  flex: 1;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

.multi-select-text.placeholder {
  color: var(--text-secondary);
}

.multi-select-arrow {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  transition: transform var(--transition-normal);
  margin-left: var(--spacing-2);
}

.multi-select-options {
  position: fixed; /* 改为fixed定位，实现真正的浮窗效果 */
  top: auto; /* 将通过JavaScript动态设置 */
  left: auto; /* 将通过JavaScript动态设置 */
  width: auto; /* 将通过JavaScript动态设置 */
  background: var(--bg-tertiary);
  border: 1px solid var(--color-primary);
  border-radius: var(--radius-md); /* 完整圆角 */
  box-shadow:
    var(--neu-shadow-outset),
    0 8px 32px rgba(0, 0, 0, 0.3), /* 增强阴影效果 */
    0 4px 16px rgba(247, 92, 244, 0.2); /* 紫色光晕 */
  max-height: 250px; /* 增加最大高度 */
  overflow-y: auto;
  z-index: 9999; /* 极高的z-index确保在最上层 */
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px) scale(0.95); /* 添加缩放效果 */
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px); /* 毛玻璃背景 */
  -webkit-backdrop-filter: blur(10px);
}

.multi-select-options.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1); /* 完整显示状态 */
}

.multi-select-option {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-3);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  min-height: 44px; /* 触摸友好 */
  border-bottom: 1px solid var(--border-color);
  -webkit-user-select: none;
  user-select: none;
}

.multi-select-option:last-child {
  border-bottom: none;
}

.multi-select-option:hover {
  background: var(--bg-secondary);
}

.multi-select-option:focus {
  outline: none;
  background: var(--bg-secondary);
  box-shadow: inset 2px 0 0 var(--color-primary);
}

.multi-select-option:active {
  background: var(--color-gray-200);
}

.multi-select-checkbox {
  width: 16px;
  height: 16px;
  min-width: 16px;
  min-height: 16px;
  max-width: 16px;
  max-height: 16px;
  margin-right: var(--spacing-2);
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: 3px;
  position: relative;
  flex-shrink: 0;
  flex-grow: 0;
  transition: all var(--transition-fast);
  box-sizing: border-box;
}

.multi-select-checkbox:hover {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 1px rgba(247, 92, 244, 0.2);
}

.multi-select-checkbox:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(247, 92, 244, 0.3);
}

.multi-select-checkbox:checked {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.multi-select-checkbox:checked::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.multi-select-label {
  flex: 1;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  cursor: pointer;
  line-height: 1.4;
  text-align: left;
}

/* 隐藏原始select元素，保持表单兼容性 */
.multi-select-dropdown select {
  display: none !important;
}

/* 板块样式 - 内容自动展开优化 */
.panel {
  background: var(--bg-tertiary);
  border: none;
  box-shadow: var(--neu-shadow-outset);
  display: flex;
  flex-direction: column;
  overflow: visible;
  position: relative;
  transition: box-shadow var(--transition-normal);
  /* 移除固定最小高度，改为动态计算 */
  min-height: var(--panel-min-height, auto);
  height: auto; /* 允许内容驱动高度 */
}

/* 板块边缘贴合效果 */
.panel:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.panel:not(:last-child) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.panel:first-child {
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.panel:last-child {
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.panel:only-child {
  border-radius: var(--radius-lg);
}

/* 板块悬停效果 */
.panel:hover {
  box-shadow: var(--neu-shadow-hover);
}

/* 键盘焦点效果 */
.panel.keyboard-focused {
  box-shadow: var(--neu-shadow-hover), 0 0 0 3px rgba(247, 92, 244, 0.3);
  outline: none;
}

.panel.keyboard-focused::before {
  content: '⌨️ 使用 Ctrl+↑/↓ 调整高度';
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  z-index: 1000;
  opacity: 0.9;
  pointer-events: none;
}

/* 板块内容区域 - 内容自动展开优化 */
.panel-content {
  flex: 1;
  padding: var(--spacing-2); /* 从spacing-3减少到spacing-2 */
  overflow: auto; /* 在内容过多时显示滚动条 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1); /* 从spacing-2减少到spacing-1 */
  min-height: 0;
  max-height: 500px; /* 设置合理的最大高度，防止过度扩展 */
  /* 确保内容完全可见 */
  width: 100%;
  box-sizing: border-box;
}

/* 板块拖拽手柄 - 触屏优化 */
.panel .resize-handle-bottom {
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 8px; /* 增加高度以改善触摸体验 */
  cursor: ns-resize;
  background: linear-gradient(90deg, transparent 20%, var(--color-primary) 50%, transparent 80%);
  opacity: 0;
  transition: all var(--transition-normal);
  z-index: 10;
  border-radius: var(--radius-sm);
  /* 触屏优化 */
  touch-action: pan-y; /* 只允许垂直拖拽 */
  -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
}

/* 触屏设备上的拖拽手柄 */
@media (pointer: coarse) {
  .panel .resize-handle-bottom {
    height: 12px; /* 触屏设备上更大的触摸目标 */
    opacity: 0.3; /* 在触屏设备上默认可见 */
  }

  .panel:hover .resize-handle-bottom,
  .panel .resize-handle-bottom:active {
    opacity: 0.8;
  }
}

.panel:hover .resize-handle-bottom {
  opacity: 0.6;
}

.panel .resize-handle-bottom:hover {
  opacity: 1 !important;
  background: var(--color-primary-hover);
  transform: scaleY(1.5);
}

.panel .resize-handle-bottom::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 2px;
  background: var(--color-white);
  border-radius: 1px;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.panel .resize-handle-bottom:hover::before {
  opacity: 0.9;
}

/* 最后一个板块不显示拖拽手柄 */
.panel:last-child .resize-handle-bottom {
  display: none;
}

/* 田字格项目 - Neumorphism效果 */
.grid-item {
  background: var(--bg-tertiary);
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--neu-shadow-outset);
  display: flex;
  flex-direction: column;
  overflow: visible; /* 改为visible以防止内容被裁切 */
  position: relative;
  transition: box-shadow var(--transition-normal);
  /* 移除resize属性，改用自定义拖拽系统 */
  min-width: var(--min-col-width, 280px);  /* 使用CSS变量 */
  min-height: var(--min-row-height, 200px); /* 使用CSS变量 */
  max-width: calc(100vw - 40px);  /* 防止超出视口 */
  max-height: calc(100vh - 200px); /* 防止超出视口 */
}

/* 网格区域指定 */
.grid-item[data-grid-area="input"] {
  grid-area: input;
}

.grid-item[data-grid-area="basic"] {
  grid-area: basic;
}

.grid-item[data-grid-area="trip"] {
  grid-area: trip;
}

.grid-item[data-grid-area="config"] {
  grid-area: config;
}

/* 田字格项目悬停效果 */
.grid-item:hover {
  box-shadow: var(--neu-shadow-hover);
}

/* 田字格拖拽手柄 - 增强版 */
.grid-item {
  position: relative;
}

.resize-handle {
  position: absolute;
  background: var(--color-primary);
  opacity: 0;
  transition: all var(--transition-normal);
  z-index: 10;
  border-radius: var(--radius-sm);
}

.grid-item:hover .resize-handle {
  opacity: 0.6;
}

.resize-handle:hover {
  opacity: 1 !important;
  background: var(--color-primary-hover);
  transform: scale(1.1);
}

.resize-handle:active {
  background: var(--color-primary);
  transform: scale(1.2);
  box-shadow: 0 0 0 2px rgba(247, 92, 244, 0.3);
}

/* 右边缘拖拽手柄 - 禁用列宽调整 */
.resize-handle-right {
  display: none; /* 暂时禁用列宽调整以解决独立性问题 */
}

/* 底边缘拖拽手柄 - 调整行高 */
.resize-handle-bottom {
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 6px;
  cursor: ns-resize;
  data-resize-type: "row";
  /* 增强视觉指示 */
  background: linear-gradient(90deg, transparent 20%, var(--color-primary) 50%, transparent 80%);
}

.resize-handle-bottom::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 2px;
  background: var(--color-white);
  border-radius: 1px;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.resize-handle-bottom:hover::before {
  opacity: 0.9;
}

/* 右下角拖拽手柄 - 只调整行高 */
.resize-handle-corner {
  bottom: -3px;
  right: -3px;
  width: 12px;
  height: 12px;
  cursor: ns-resize; /* 改为只支持垂直调整 */
  border-radius: 50%;
  data-resize-type: "both";
  /* 增强视觉指示 */
  background: radial-gradient(circle, var(--color-primary) 30%, transparent 70%);
}

.resize-handle-corner::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background: var(--color-white);
  border-radius: 50%;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.resize-handle-corner:hover::before {
  opacity: 1;
}

/* 拖拽时的视觉反馈 */
.grid-item.resizing {
  box-shadow: var(--neu-shadow-pressed), 0 0 0 2px var(--color-primary);
  z-index: 20;
  transition: none; /* 拖拽时禁用过渡动画 */
}

.grid-container.resizing {
  user-select: none;
  -webkit-user-select: none;
  cursor: grabbing;
}

/* 网格调整过渡动画 */
.grid-container:not(.resizing) {
  transition: grid-template-columns 0.3s ease-out, grid-template-rows 0.3s ease-out;
}

/* 拖拽手柄增强视觉效果 */
.resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 2px;
  background: var(--color-white);
  border-radius: 50%;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.resize-handle:hover::before {
  opacity: 0.8;
}

/* 网格线指示器（调整时显示） */
.grid-container.resizing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(to right, transparent calc(var(--grid-col-1, 50%) - 1px), var(--color-primary) calc(var(--grid-col-1, 50%) - 1px), var(--color-primary) calc(var(--grid-col-1, 50%) + 1px), transparent calc(var(--grid-col-1, 50%) + 1px)),
    linear-gradient(to bottom, transparent calc(var(--grid-row-1, 50%) - 1px), var(--color-primary) calc(var(--grid-row-1, 50%) - 1px), var(--color-primary) calc(var(--grid-row-1, 50%) + 1px), transparent calc(var(--grid-row-1, 50%) + 1px));
  opacity: 0.3;
  pointer-events: none;
  z-index: 5;
}

/* 田字格项目头部 */
.grid-item .section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.grid-item .section-header h3 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 田字格项目内容 - 改善内容保护 */
.grid-item .input-card,
.grid-item .form-card {
  flex: 1;
  padding: var(--spacing-3);
  overflow: auto; /* 改为auto，允许滚动以防止内容被裁切 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  min-height: 0; /* 允许flex子项收缩 */
  /* 添加内容保护 */
  max-height: calc(100% - var(--spacing-6)); /* 确保不超出容器 */
}

/* 输入卡片特殊样式优化 */
.input-card {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  position: relative;
}

.input-card .form-group {
  margin-bottom: var(--spacing-2);
}

.input-card .form-group:last-child {
  margin-bottom: 0;
}

/* 输入卡片内的标签样式 */
.input-card label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
  display: block;
}

/* 订单输入区域特殊样式 */
.input-section textarea {
  min-height: 100px;
  resize: vertical;
}

/* Textarea容器 - 支持内嵌按钮 */
.textarea-container {
  position: relative;
  display: flex;
  flex-direction: column;
}

.textarea-container textarea {
  padding-right: 45px; /* 为内嵌按钮留出空间 */
  resize: vertical;
  min-height: 100px;
}

/* Textarea内嵌上传按钮 */
.textarea-upload-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--neu-shadow-subtle);
  z-index: 10;
}

.textarea-upload-button:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--neu-shadow-outset);
}

.textarea-upload-button:active {
  transform: translateY(0);
  box-shadow: var(--neu-shadow-inset);
}

.textarea-upload-button .upload-icon {
  font-size: 0.9rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.input-section .input-actions {
  margin-top: 0;
  padding-top: var(--spacing-2);
  border-top: 1px solid var(--border-color);
}

/* 服务配置区域 */
.service-config-section {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  margin-bottom: var(--spacing-4);
}

.service-config-section .section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.service-config-section .section-header h3 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.service-config-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
}

.config-left,
.config-right {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

/* ========================================
   服务配置价格样式重构 - 现代化设计
   ======================================== */

/* 价格输入组 - 重构版本 */
.price-input-group {
  display: flex;
  align-items: stretch;
  gap: 0;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  box-shadow: var(--neu-shadow-inset);
  position: relative;
}

.price-input-group:hover {
  border-color: var(--color-primary);
  box-shadow: var(--neu-shadow-inset), 0 0 0 1px rgba(247, 92, 244, 0.3);
}

.price-input-group:focus-within {
  border-color: var(--color-primary);
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(247, 92, 244, 0.2);
}

/* 价格输入框 */
.price-input-group input {
  flex: 1;
  height: 48px;
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  outline: none;
  transition: all var(--transition-normal);
}

.price-input-group input::placeholder {
  color: var(--text-tertiary);
  font-weight: 400;
  font-size: var(--font-size-base);
}

.price-input-group input:focus {
  outline: none;
  color: var(--color-primary);
}

/* 货币选择器 */
.price-input-group select {
  width: 90px;
  height: 48px;
  padding: var(--spacing-2) var(--spacing-3);
  border: none;
  border-left: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  outline: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  flex-shrink: 0;
}

.price-input-group select:hover {
  background: var(--color-gray-200);
  color: var(--text-primary);
}

.price-input-group select:focus {
  outline: none;
  background: var(--color-primary);
  color: white;
  border-left-color: var(--color-primary);
}

/* 价格输入组增强效果 */
.price-input-group::before {
  content: '💰';
  position: absolute;
  left: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--font-size-lg);
  opacity: 0.6;
  pointer-events: none;
  transition: all var(--transition-normal);
}

.price-input-group:focus-within::before {
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

.price-input-group input {
  padding-left: calc(var(--spacing-4) + 30px); /* 为图标留出空间 */
}

/* 价格验证状态 */
.price-input-group.valid {
  border-color: var(--color-success);
  box-shadow: var(--neu-shadow-inset), 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.price-input-group.valid::after {
  content: '✓';
  position: absolute;
  right: 100px; /* 在货币选择器左边 */
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-success);
  font-weight: bold;
  font-size: var(--font-size-lg);
}

.price-input-group.invalid {
  border-color: var(--color-error);
  box-shadow: var(--neu-shadow-inset), 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.price-input-group.invalid::after {
  content: '⚠️';
  position: absolute;
  right: 100px;
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--font-size-lg);
}

/* 价格转换显示 */
.price-conversion-display {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background: linear-gradient(135deg, rgba(247, 92, 244, 0.1), rgba(224, 64, 251, 0.05));
  border: 1px solid rgba(247, 92, 244, 0.2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.price-conversion-display .conversion-icon {
  color: var(--color-primary);
  opacity: 0.8;
}

.price-conversion-display .conversion-text {
  flex: 1;
}

.price-conversion-display .conversion-rate {
  font-weight: 600;
  color: var(--color-primary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .price-input-group {
    flex-direction: column;
    height: auto;
  }
  
  .price-input-group select {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--radius-md) var(--radius-md);
  }
  
  .price-input-group input {
    border-radius: var(--radius-md) var(--radius-md) 0 0;
  }
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

/* 操作按钮区域 - 跨列布局优化 */
.action-section {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
  padding: var(--spacing-3); /* 减少内边距以适应grid */
  margin-top: var(--spacing-2); /* 与上方内容的间距 */
  margin-bottom: 0; /* 移除底部边距 */
}

.action-section .form-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  margin: 0;
  padding: 0;
  border: none;
}

.action-section .btn {
  min-width: 150px;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: 600;
}

/* 数据异常提示样式 */
.data-issues {
  color: var(--text-primary);
}

.issue-item {
  color: var(--color-warning);
  margin: var(--spacing-1) 0;
}

.issue-note {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-3);
  font-style: italic;
}

/* 区域样式 */
.input-section,
.preview-section,
.console-section {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-2); /* 从spacing-3减少到spacing-2 */
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  font-size: var(--font-size-base); /* 减少区域标题大小 */
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.section-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.input-card,
.preview-card,
.console-card {
  padding: var(--spacing-2); /* 从spacing-4减少到spacing-2 */
}

/* 实时分析相关样式 */
.realtime-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.realtime-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  background: var(--color-primary-light);
  color: var(--color-primary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  width: fit-content;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: var(--spacing-3); /* 减少输入操作间距 */
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.image-upload-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.image-upload-controls .image-upload-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);  /* 减少圆角 */
  padding: var(--spacing-1) var(--spacing-2);  /* 大幅减少内边距 */
  font-size: var(--font-size-xs);  /* 减少字体大小 */
  font-weight: 500;  /* 减少字重 */
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);  /* 减少图标与文字间距 */
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);  /* 减少阴影 */
  position: relative;
  overflow: hidden;
  max-width: 100px;  /* 限制最大宽度 */
  height: 28px;      /* 固定高度 */
}

.image-upload-controls .image-upload-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.image-upload-controls .image-upload-button:hover::before {
  left: 100%;
}

.image-upload-controls .image-upload-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.image-upload-controls .upload-icon {
  font-size: 0.8rem;  /* 减少图标尺寸 */
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 表单样式 - 大幅紧凑化 */
.form-group {
  margin-bottom: var(--spacing-1); /* 从spacing-2减少到spacing-1 */
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-1); /* 减少标签下方间距 */
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  box-shadow: var(--neu-shadow-inset);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(247, 92, 244, 0.2);
  background: var(--bg-primary);
}

.form-group input:hover:not(:focus),
.form-group select:hover:not(:focus),
.form-group textarea:hover:not(:focus) {
  box-shadow: var(--neu-shadow-subtle);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(244, 67, 54, 0.2);
}

.field-error {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px; /* 减少文本域最小高度 */
}

.form-section {
  margin-bottom: var(--spacing-4); /* 从spacing-8大幅减少到spacing-4 */
  padding-bottom: var(--spacing-3); /* 从spacing-6减少到spacing-3 */
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h4 {
  font-size: var(--font-size-base); /* 减少表单区域标题大小 */
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-3); /* 减少标题下方间距 */
  padding-bottom: var(--spacing-1);
  border-bottom: 2px solid var(--color-primary);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 改为三列固定布局 */
  gap: var(--spacing-2); /* 进一步减少网格间距 */
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* 复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  user-select: none;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
  cursor: pointer;
}

.checkbox-text {
  cursor: pointer;
}

.checkbox-label:hover {
  color: var(--text-primary);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  box-shadow: var(--neu-shadow-outset);
}

.btn:hover:not(:disabled) {
  box-shadow: var(--neu-shadow-hover);
  transform: translateY(-1px);
}

.btn:active:not(:disabled) {
  box-shadow: var(--neu-shadow-pressed);
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: var(--neu-shadow-subtle);
}

.btn-primary {
  background: var(--color-primary-gradient);
  color: var(--color-white);
  box-shadow: var(--neu-shadow-outset), 0 4px 15px rgba(247, 92, 244, 0.3);
}

.btn-primary:hover:not(:disabled) {
  box-shadow: var(--neu-shadow-hover), 0 8px 25px rgba(247, 92, 244, 0.4);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--color-secondary);
  color: var(--color-white);
  border-color: var(--color-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--border-hover);
}

.btn-sm {
  padding: var(--spacing-1) var(--spacing-2); /* 减少小按钮padding */
  font-size: var(--font-size-xs);
}

.btn-icon {
  width: 32px;  /* 减少图标按钮大小 */
  height: 32px;
  padding: 0;
  border-radius: 50%;
}

/* 主题切换区域 */
.theme-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;  /* 防止收缩 */
  min-width: 0;    /* 允许内部元素收缩 */
}

/* 语言选择下拉菜单 - Neumorphism风格 */
.language-select {
  background: var(--bg-tertiary);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--neu-shadow-outset);
  min-width: 70px;  /* 减少最小宽度 */
  margin-right: var(--spacing-2);
  flex-shrink: 0;   /* 防止收缩 */
}

.language-select:hover {
  box-shadow: var(--neu-shadow-hover);
}

.language-select:focus {
  outline: none;
  box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(247, 92, 244, 0.2);
}

.language-select option {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: var(--spacing-2);
}

.loading-spinner {
  font-size: var(--font-size-base);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 操作按钮组 */
.form-actions {
  display: flex;
  gap: var(--spacing-2); /* 减少按钮组间距 */
  margin-top: var(--spacing-4); /* 减少顶部间距 */
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
}

/* 日志控制台 */
.log-console {
  background: var(--color-gray-900);
  color: var(--color-gray-100);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  padding: var(--spacing-3); /* 减少控制台padding */
  border-radius: var(--radius-md);
  height: 250px; /* 减少控制台高度 */
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.log-entry {
  margin-bottom: var(--spacing-1); /* 减少日志条目间距 */
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  border-left: 3px solid transparent;
}

.log-entry.info {
  background: rgba(6, 182, 212, 0.1);
  border-left-color: var(--color-info);
}

.log-entry.success {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: var(--color-success);
}

.log-entry.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: var(--color-warning);
}

.log-entry.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: var(--color-error);
}

.log-timestamp {
  color: var(--color-gray-400);
  font-size: var(--font-size-xs);
}

/* 切换开关 */
.toggle-switch {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.toggle-switch input {
  display: none;
}

.toggle-slider {
  width: 40px; /* 减少开关大小 */
  height: 20px;
  background: var(--color-gray-300);
  border-radius: 10px;
  position: relative;
  transition: background-color var(--transition-fast);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px; /* 减少开关按钮大小 */
  height: 16px;
  border-radius: 50%;
  background: var(--color-white);
  top: 2px;
  left: 2px;
  transition: transform var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.toggle-switch input:checked + .toggle-slider {
  background: var(--color-primary);
}

.toggle-switch input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

/* 状态栏 */
.status-bar {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-1) var(--spacing-4); /* 减少状态栏padding */
  font-size: var(--font-size-xs);
}

.status-info {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: var(--spacing-3);
}

.status-item {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* 模态框 - 重点优化，确保订单预览无滚动显示 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000; /* 提高z-index确保在预览浮窗之上 */
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  padding: var(--spacing-4); /* 添加模态框外边距 */
}

.modal-content {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  width: 100%;
  max-width: 900px; /* 增加最大宽度 */
  max-height: 90vh; /* 减少最大高度，确保有边距 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少模态框头部padding */
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-header h3 {
  font-size: var(--font-size-base); /* 减少模态框标题大小 */
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-3); /* 大幅减少模态框body padding */
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.modal-footer {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少模态框footer padding */
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2); /* 减少按钮间距 */
  flex-shrink: 0;
}

/* 订单预览专用样式 - 极致紧凑化 */
.order-preview {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
}

.order-preview h4 {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-2);
  color: var(--color-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-1);
}

.order-preview h5 {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
  font-weight: 600;
}

.order-preview p {
  margin: 0 0 var(--spacing-1) 0; /* 极致减少段落间距 */
  line-height: var(--line-height-tight);
}

/* 这些样式已移动到浮窗样式部分，避免重复 */



/* API结果显示 */
.api-result-block {
  margin-top: var(--spacing-3);
}

.api-result-json {
  background: #222 !important;
  color: #fff !important;
  padding: var(--spacing-2) !important;
  border-radius: var(--radius-md) !important;
  max-height: 200px !important; /* 限制高度 */
  overflow: auto !important;
  font-size: var(--font-size-xs) !important;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
}

/* 成功/错误消息样式 */
.success-message,
.error-message {
  margin-bottom: var(--spacing-3);
}

.success-message p,
.error-message p {
  margin-bottom: var(--spacing-1);
}

/* 订单成功样式 */
.order-success-header {
  text-align: center;
  margin-bottom: var(--spacing-3);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.order-success-header h4 {
  color: var(--color-success);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-lg);
}

.order-id-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-2);
}

.order-id-highlight {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
  font-family: 'Courier New', monospace;
  background: var(--color-primary-light);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-primary);
}

.order-details {
  background: var(--bg-secondary);
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-3);
}

.order-details p {
  margin: var(--spacing-1) 0;
  font-size: var(--font-size-sm);
}

/* 响应式设计 - 动态高度适配优化 */
@media (max-width: 1024px) {
  .main-content {
    max-width: 100vw;
    padding: var(--spacing-3) var(--spacing-2);
    height: calc(100vh - var(--header-height, 70px));
  }
  
  .input-card {
    padding: var(--spacing-3);
  }
  
  .form-section {
    margin-bottom: var(--spacing-3);
    padding-bottom: var(--spacing-2);
  }
  
  .form-group {
    margin-bottom: var(--spacing-2);
  }
  
  .form-actions,
  .input-actions {
    margin-top: var(--spacing-3);
    padding-top: var(--spacing-2);
    gap: var(--spacing-2);
  }
  
  .workspace {
    gap: var(--spacing-3);
  }

  /* 田字格在平板上改为2x2布局，但高度自适应 */
  .grid-container {
    height: auto;
    grid-template-rows: auto auto;
    /* 平板上保持灵活网格 */
    grid-template-columns: var(--grid-col-1, 1fr) var(--grid-col-2, 1fr);
    grid-template-rows: var(--grid-row-1, auto) var(--grid-row-2, auto);
  }

  .grid-item {
    min-height: 300px;
  }

  /* 服务配置区域在平板上保持左右布局 */
  .service-config-container {
    gap: var(--spacing-3);
  }

  .modal-content {
    max-width: 95vw;
  }


}

/* 超宽屏优化 (21:9 及以上) */
@media (min-aspect-ratio: 21/9) {
  .grid-container {
    max-width: 1400px;
    margin: 0 auto;
  }

  .main-content {
    max-width: 1400px;
  }
}

/* 传统屏幕比例优化 (4:3) */
@media (max-aspect-ratio: 4/3) {
  .grid-container {
    height: calc(100vh - 160px);
  }

  .grid-item {
    min-height: 120px;
  }
}

/* 标准宽屏优化 (16:9) */
@media (min-aspect-ratio: 16/9) and (max-aspect-ratio: 16/8) {
  .grid-container {
    height: calc(100vh - 140px);
  }
}

/* 竖屏显示优化 - 解决内容被截断问题 */
@media (orientation: portrait) {
  /* 确保body和html在竖屏时能够正确滚动 */
  html, body {
    height: auto;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .grid-container-new {
    min-height: calc(100vh - 150px); /* 减少顶部空间 */
    max-height: none; /* 完全移除高度限制 */
    height: auto; /* 完全由内容驱动 */
    overflow: visible; /* 确保内容可见 */
  }

  /* 竖屏时减少板块最小高度，确保所有内容可见 */
  .column-left {
    grid-template-rows:
      minmax(150px, auto)  /* 订单输入 */
      minmax(120px, auto)  /* 行程信息 */
      minmax(80px, auto)   /* 特殊需求 */
      minmax(80px, auto);  /* 额外要求 */
  }

  .column-right {
    grid-template-rows:
      minmax(120px, auto)  /* 基本信息 */
      minmax(140px, auto)  /* 客户信息 */
      minmax(160px, auto); /* 服务配置 */
  }

  /* 竖屏时板块内容最大高度调整 */
  .panel-content {
    max-height: 300px; /* 减少最大高度 */
    overflow-y: auto; /* 确保滚动可用 */
  }

  /* 操作按钮区域在竖屏时的优化 */
  .action-section {
    padding: var(--spacing-2);
    margin-top: var(--spacing-1);
    position: sticky; /* 确保操作按钮始终可见 */
    bottom: 0;
    z-index: 10;
  }

  /* 确保主内容区域在竖屏时可以滚动 */
  .main-content {
    overflow-y: auto;
    height: 100vh;
    padding-bottom: 80px; /* 为操作按钮留出空间 */
  }
}

/* 中等屏幕优化 (平板和小桌面) - 保持两列布局 */
@media (max-width: 1024px) and (min-width: 769px) {
  .grid-container-new {
    grid-template-columns: 1fr 1fr; /* 强制保持两列 */
    min-height: calc(100vh - 180px);
    max-height: none; /* 移除高度限制，允许内容完全展开 */
    height: auto; /* 内容驱动高度 */
    gap: var(--spacing-1);
  }

  /* 调整板块最小高度以适应中等屏幕 */
  .column-left {
    grid-template-rows:
      minmax(160px, auto)  /* 订单输入 */
      minmax(140px, auto)  /* 行程信息 */
      minmax(100px, auto)  /* 特殊需求 */
      minmax(80px, auto);  /* 额外要求 */
  }

  .column-right {
    grid-template-rows:
      minmax(140px, auto)  /* 基本信息 */
      minmax(160px, auto)  /* 客户信息 */
      minmax(180px, auto); /* 服务配置 */
  }

  .panel-content {
    max-height: 350px;
    padding: var(--spacing-1);
  }
}

@media (max-width: 768px) {
  html {
    font-size: 15px;
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-2);
    padding: 0 var(--spacing-2);
  }

  .app-title {
    font-size: var(--font-size-lg);
  }

  /* 移动端顶部控制栏优化 */
  .header-controls {
    justify-content: center;  /* 居中对齐 */
    flex-wrap: wrap;
    gap: var(--spacing-2);
  }

  .persistent-email {
    order: 3;  /* 邮箱输入放到最后 */
    width: 100%;  /* 全宽显示 */
    margin-right: 0;
  }

  .user-info {
    order: 2;  /* 用户信息第二 */
    flex-wrap: wrap;
    gap: var(--spacing-1);
  }

  .theme-toggle {
    order: 1;  /* 主题切换优先显示 */
    flex-shrink: 0;
  }

  .language-select {
    min-width: 60px;  /* 进一步减少移动端宽度 */
  }
  
  .main-content {
    padding: var(--spacing-2) var(--spacing-1);
    height: calc(100vh - var(--header-height, 60px));
  }

  /* 移动端保持两列布局但优化尺寸 */
  .grid-container-new {
    grid-template-columns: 1fr 1fr; /* 保持两列布局 */
    grid-template-rows: minmax(0, 1fr) auto; /* 主内容自适应 + 操作按钮固定 */
    height: auto;
    min-height: calc(100vh - 100px); /* 进一步减少最小高度 */
    max-height: none; /* 允许完全展开 */
    gap: var(--spacing-1); /* 减少间距 */
    padding: var(--spacing-1); /* 减少内边距 */
    overflow: visible;
  }

  /* 移动端板块最小高度调整 */
  .column-left {
    grid-template-rows:
      minmax(120px, auto)  /* 订单输入 */
      minmax(100px, auto)  /* 行程信息 */
      minmax(80px, auto)   /* 特殊需求 */
      minmax(60px, auto);  /* 额外要求 */
  }

  .column-right {
    grid-template-rows:
      minmax(100px, auto)  /* 基本信息 */
      minmax(120px, auto)  /* 客户信息 */
      minmax(140px, auto); /* 服务配置 */
  }

  /* 移动端隐藏拖拽手柄 */
  .panel .resize-handle-bottom {
    display: none;
  }

  /* 移动端板块样式调整 */
  .panel {
    min-height: 120px;
    margin-bottom: var(--spacing-2);
  }

  .panel:not(:last-child) {
    border-radius: var(--radius-lg);
  }

  .grid-item {
    min-height: 200px;
  }

  /* 服务配置区域在手机上改为单列布局，但保持简洁 */
  .service-config-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .config-left,
  .config-right {
    min-height: auto;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }
  
  .section-header {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-start;
    padding: var(--spacing-2);
  }
  
  .section-controls {
    width: 100%;
    justify-content: flex-end;
    flex-wrap: wrap;
    gap: var(--spacing-1);
  }
  
  .section-controls .btn {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-2);
  }
  
  .form-actions,
  .input-actions {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  /* 移动端操作按钮区域优化 */
  .action-section {
    padding: var(--spacing-2);
    margin-top: var(--spacing-1);
  }

  .action-section .form-actions {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .action-section .btn {
    min-width: auto;
    width: 100%;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }

  /* 移动端多选下拉菜单优化 */
  .multi-select-trigger {
    padding: var(--spacing-3);
    font-size: var(--font-size-base);
  }

  .multi-select-options {
    max-height: 250px; /* 移动端增加最大高度 */
  }

  .multi-select-option {
    padding: var(--spacing-3);
    min-height: 48px; /* 移动端增加触摸目标 */
  }

  .multi-select-checkbox {
    width: 20px;
    height: 20px;
    margin-right: var(--spacing-3);
  }
  
  .input-actions {
    align-items: stretch;
  }

  .image-upload-controls {
    justify-content: center;
  }

  .image-upload-controls .image-upload-button {
    padding: var(--spacing-3) var(--spacing-4);
    min-width: 150px;
    justify-content: center;
  }
  
  /* 特殊要求区域移动端样式 */
  .requirements-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
  }

  .requirements-container-integrated {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .checkbox-group-vertical {
    gap: var(--spacing-2);
  }

  /* Textarea内嵌按钮移动端优化 */
  .textarea-upload-button {
    width: 28px;
    height: 28px;
    top: 6px;
    right: 6px;
  }

  .textarea-container textarea {
    padding-right: 40px;
  }
  
  .login-card {
    margin: var(--spacing-2);
    padding: var(--spacing-4);
  }
  
  .modal {
    padding: var(--spacing-2);
  }

  .modal-content {
    width: 100%;
    max-width: 100vw;
    max-height: 95vh;
  }
  
  .modal-body {
    padding: var(--spacing-2);
  }

  .modal-header,
  .modal-footer {
    padding: var(--spacing-2);
  }

  /* 田字格内部内容的移动端优化 */
  .grid-item .section-header h3 {
    font-size: var(--font-size-sm);
  }
  
  .grid-item .input-card,
  .grid-item .form-card {
    padding: var(--spacing-2);
    gap: var(--spacing-1);
  }
  
  .grid-item .form-group {
    margin-bottom: var(--spacing-1);
  }
  
  .grid-item .form-group label {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-1);
  }
  
  .grid-item .form-group input,
  .grid-item .form-group select,
  .grid-item .form-group textarea {
    font-size: var(--font-size-sm);
    padding: var(--spacing-1) var(--spacing-2);
  }

  /* 图片上传区域移动端优化 */
  .image-upload-area {
    min-height: 60px;
  }
  
  .upload-icon {
    font-size: var(--font-size-lg);
  }
  
  .upload-primary {
    font-size: var(--font-size-xs);
  }
  
  .upload-secondary {
    font-size: 10px;
  }

  /* 订单输入区域移动端优化 */
  .input-section textarea {
    min-height: 80px;
    font-size: var(--font-size-sm);
  }
  
  .realtime-badge {
    font-size: 10px;
    padding: var(--spacing-1);
  }


}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }
  
  .main-content {
    padding: var(--spacing-1);
  }
  
  /* 超小屏幕仍保持田字格布局，但进一步紧凑化 */
  .grid-container {
    gap: var(--spacing-1);
  }

  .grid-item {
    min-height: 180px;
  }
  
  .grid-item .section-header {
    padding: var(--spacing-1) var(--spacing-2);
  }
  
  .grid-item .section-header h3 {
    font-size: 12px;
  }
  
  .grid-item .input-card,
  .grid-item .form-card {
    padding: var(--spacing-1);
    gap: var(--spacing-1);
  }
  
  .form-section {
    margin-bottom: var(--spacing-1);
    padding-bottom: var(--spacing-1);
  }
  
  .form-group {
    margin-bottom: var(--spacing-1);
  }
  
  .form-group label {
    font-size: 11px;
    margin-bottom: 2px;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    font-size: 12px;
    padding: var(--spacing-1);
  }
  
  .form-actions,
  .input-actions {
    margin-top: var(--spacing-1);
    padding-top: var(--spacing-1);
    gap: var(--spacing-1);
  }
  
  .workspace {
    gap: var(--spacing-1);
  }

  .section-controls .btn {
    font-size: 10px;
    padding: var(--spacing-1);
  }

  /* 图片上传区域超小屏优化 */
  .image-upload-area {
    min-height: 50px;
  }
  
  .upload-icon {
    font-size: var(--font-size-base);
  }
  
  .upload-primary {
    font-size: 10px;
  }
  
  .upload-secondary {
    font-size: 8px;
  }

  /* 文本区域超小屏优化 */
  .input-section textarea {
    min-height: 60px;
    font-size: 12px;
  }
  
  .realtime-badge {
    font-size: 8px;
    padding: 2px var(--spacing-1);
  }

  .realtime-info small {
    font-size: 8px;
  }

  .modal {
    padding: var(--spacing-1);
  }
  
  .modal-content {
    width: 100%;
    max-width: 100vw;
    max-height: 98vh;
    border-radius: var(--radius-md);
  }
  
  .modal-body {
    padding: var(--spacing-2);
  }

  .modal-header,
  .modal-footer {
    padding: var(--spacing-2);
  }

  .section-header {
    padding: var(--spacing-2);
  }

  .preview-section p strong {
    min-width: 60px;
    font-size: var(--font-size-xs);
  }

  .api-result-json {
    max-height: 150px !important;
  }

  /* 极小屏幕的特殊优化 */
  .form-section h4 {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-2);
  }

  .order-preview h5 {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-1);
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px); /* 减少动画距离 */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.slide-in {
  animation: slideIn var(--transition-normal) ease-out;
}

/* 实用类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }
.show { display: block !important; }
.show.flex { display: flex !important; }
.hidden-field { display: none !important; }

/* OTA渠道输入组合样式 */
.ota-channel-inputs {
  display: flex;
  gap: var(--spacing-2);
  flex: 1;
}

.ota-channel-inputs select,
.ota-channel-inputs input {
  flex: 1;
}

.mt-4 { margin-top: var(--spacing-4); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mr-4 { margin-right: var(--spacing-4); }
.ml-4 { margin-left: var(--spacing-4); }

.p-4 { padding: var(--spacing-4); }
.pt-4 { padding-top: var(--spacing-4); }
.pb-4 { padding-bottom: var(--spacing-4); }
.pr-4 { padding-right: var(--spacing-4); }
.pl-4 { padding-left: var(--spacing-4); }

/* 预览卡片额外优化 */
.preview-card {
  padding: var(--spacing-3);
}

/* 紧凑模式的额外CSS变量 */
:root {
  --compact-spacing: var(--spacing-1);
  --compact-gap: var(--spacing-2);
}



/* ========================================
   可编辑字段样式
   ======================================== */

/* 字段容器 */
.field-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 编辑按钮 */
.edit-field-btn {
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-1);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

.edit-field-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--color-primary);
  color: var(--color-primary);
  opacity: 1;
  transform: scale(1.05);
}

.edit-field-btn:active {
  transform: scale(0.95);
}

/* 字段编辑状态 */
.editable-field.editing .edit-field-btn {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
  opacity: 1;
}

.editable-field.editing input,
.editable-field.editing select,
.editable-field.editing textarea {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
  background: var(--color-primary-light);
}

/* 字段保存和取消按钮 */
.field-actions {
  display: none;
  gap: var(--spacing-1);
  margin-top: var(--spacing-1);
}

.editable-field.editing .field-actions {
  display: flex;
}

.field-action-btn {
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-1) var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-xs);
  transition: all var(--transition-fast);
}

.field-action-btn.save {
  color: var(--color-success);
  border-color: var(--color-success);
}

.field-action-btn.save:hover {
  background: var(--color-success);
  color: white;
}

.field-action-btn.cancel {
  color: var(--color-error);
  border-color: var(--color-error);
}

.field-action-btn.cancel:hover {
  background: var(--color-error);
  color: white;
}



/* ========================================
   历史订单面板样式
   ======================================== */

/* 历史订单面板 */
.history-panel {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2500;
}

.history-panel.show {
  display: block;
}

/* 历史订单遮罩 */
.history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2501;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  padding: var(--spacing-4);
}

/* 历史订单内容 */
.history-content {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: historySlideIn 0.3s ease-out;
}

/* 历史订单头部 */
.history-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.history-header h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
}

.history-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 搜索区域 */
.history-search {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.search-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.search-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.search-group label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.search-group input {
  padding: var(--spacing-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.search-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

/* 统计区域 */
.history-stats {
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-3);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-2);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.stat-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-1);
}

.stat-value {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-primary);
}

/* 订单列表 */
.history-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.list-header {
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title {
  font-weight: 600;
  color: var(--text-primary);
}

.list-count {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.list-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-2);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-2);
  opacity: 0.5;
}

.empty-text {
  font-size: var(--font-size-base);
}

/* 订单项 */
.history-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-2);
  transition: all var(--transition-fast);
}

.history-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.history-item-id {
  font-weight: 600;
  color: var(--color-primary);
  font-family: monospace;
}

.history-item-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.history-item-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.history-item-field {
  display: flex;
  flex-direction: column;
}

.history-item-label {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  margin-bottom: 2px;
}

.history-item-value {
  color: var(--text-primary);
  font-weight: 500;
}

.history-item-actions {
  margin-top: var(--spacing-2);
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

/* 动画 */
@keyframes historySlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ========================================
   图片上传组件样式
   ======================================== */

/* 图片上传区域 */
.image-upload-section {
  margin-bottom: var(--spacing-4);
}

/* 图片上传区域 - 已移除独立区域，改为按钮形式 */
.image-upload-section {
  margin-bottom: var(--spacing-4);
}

/* 通用图片上传按钮样式 */
.image-upload-button {
  background: var(--color-secondary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  box-shadow: var(--shadow-sm);
}

.image-upload-button:hover {
  background: var(--color-secondary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.image-upload-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.image-upload-button:disabled {
  background: var(--color-gray-300);
  cursor: not-allowed;
  transform: none;
}

.upload-hint {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
  margin-top: var(--spacing-2);
}

.upload-content {
  display: none; /* 不再使用的旧样式 */
}

.upload-icon {
  font-size: 1rem;
}

.upload-text {
  display: none; /* 隐藏原有的文本提示 */
}

.upload-primary {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--text-primary);
}

.upload-secondary {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 上传状态 */
.upload-status {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  text-align: center;
  transition: all var(--transition-fast);
}

.upload-status.info {
  background: var(--color-info-light);
  color: var(--color-info);
  border: 1px solid var(--color-info);
}

.upload-status.success {
  background: var(--color-success-light);
  color: var(--color-success);
  border: 1px solid var(--color-success);
}

.upload-status.warning {
  background: var(--color-warning-light);
  color: var(--color-warning);
  border: 1px solid var(--color-warning);
}

.upload-status.error {
  background: var(--color-error-light);
  color: var(--color-error);
  border: 1px solid var(--color-error);
}

/* 图片预览容器 */
.image-preview-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-3);
  margin-top: var(--spacing-3);
}

.image-preview-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: all var(--transition-fast);
}

.image-preview-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.image-preview-wrapper {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-fast);
}

.image-preview-item:hover .preview-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0) 30%,
    rgba(0, 0, 0, 0) 70%,
    rgba(0, 0, 0, 0.3) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: var(--spacing-2);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.image-preview-item:hover .image-overlay {
  opacity: 1;
}

.image-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  align-self: flex-start;
}

.image-status[data-status="analyzing"] {
  background: rgba(59, 130, 246, 0.8);
}

.image-status[data-status="analyzed"] {
  background: rgba(34, 197, 94, 0.8);
}

.image-status[data-status="error"] {
  background: rgba(239, 68, 68, 0.8);
}

.image-actions {
  display: flex;
  gap: var(--spacing-1);
  align-self: flex-end;
}

.delete-image-btn {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.delete-image-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

.image-info {
  padding: var(--spacing-2);
}

.image-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-size {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-preview-container {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-2);
  }

  .image-upload-area {
    gap: var(--spacing-2);
  }

  .image-upload-button {
    padding: var(--spacing-3) var(--spacing-5);
    min-width: 180px;
    font-size: var(--font-size-sm);
  }

  .upload-icon {
    font-size: 1rem;
  }

  .upload-hint {
    font-size: var(--font-size-xs);
  }
}

/* ========================================
   价格转换组件样式
   ======================================== */

/* 价格转换提示 */
.price-conversion-note {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2);
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
}

.conversion-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-info);
}

.conversion-icon {
  font-size: var(--font-size-base);
  opacity: 0.8;
}

.conversion-text {
  font-weight: 500;
}

/* 汇率设置面板 */
.exchange-rate-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  margin-top: var(--spacing-3);
}

.exchange-rate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.exchange-rate-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.exchange-rate-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.rate-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.rate-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.rate-input {
  padding: var(--spacing-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.rate-input:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.rate-actions {
  display: flex;
  gap: var(--spacing-2);
  margin-top: var(--spacing-3);
  justify-content: flex-end;
}

/* 价格显示增强 */
.price-display {
  position: relative;
}

.price-display.has-conversion::after {
  content: "💱";
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--font-size-sm);
  opacity: 0.6;
}

/* ========================================
   多订单预览面板样式
   ======================================== */

/* 多订单面板 */
.multi-order-panel {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2600;
}

.multi-order-panel.show {
  display: block;
}

/* 多订单遮罩 */
.multi-order-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2601;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  padding: var(--spacing-4);
}

/* 多订单内容 */
.multi-order-content {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-blur);
  backdrop-filter: var(--glass-blur);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  width: 100%;
  max-width: 1400px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: multiOrderSlideIn 0.3s ease-out;
}

/* 多订单头部 */
.multi-order-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.multi-order-header h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
}

.multi-order-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.order-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
}

.order-count {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-primary);
}

.date-range {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 多订单列表 */
.multi-order-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-3);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-3);
  align-content: start;
}

/* 多订单项 */
.multi-order-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  transition: all var(--transition-fast);
  position: relative;
}

.multi-order-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.multi-order-item.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.multi-order-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.order-sequence {
  background: var(--color-primary);
  color: white;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.order-actions {
  display: flex;
  gap: var(--spacing-1);
}

.multi-order-item-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.order-field {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.order-field-label {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.order-field-value {
  color: var(--text-primary);
  font-weight: 500;
}

.order-field-value.empty {
  color: var(--text-secondary);
  font-style: italic;
}

/* 批量创建进度显示 */
.batch-create-status {
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  display: none;
  animation: slideDown 0.3s ease-out;
}

.batch-create-status:not(:empty) {
  display: block;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.progress-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.progress-icon {
  font-size: var(--font-size-lg);
  animation: spin 2s linear infinite;
}

.progress-label {
  font-weight: 600;
  color: var(--text-primary);
}

.progress-actions {
  display: flex;
  gap: var(--spacing-1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.progress-text {
  color: var(--text-primary);
  font-weight: 500;
}

.progress-percent {
  color: var(--color-primary);
  font-weight: 600;
  font-size: var(--font-size-base);
}

.progress-bar {
  width: 100%;
  height: 10px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-success));
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.progress-count {
  font-weight: 500;
}

.progress-eta {
  font-style: italic;
}

/* 进度动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 验证结果样式 */
.validation-results {
  max-height: 60vh;
  overflow-y: auto;
}

.validation-summary {
  display: flex;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  padding: var(--spacing-2);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.valid-count {
  color: var(--color-success);
  font-weight: 600;
}

.invalid-count {
  color: var(--color-error);
  font-weight: 600;
}

.invalid-orders {
  margin-bottom: var(--spacing-3);
}

.invalid-order-item {
  background: var(--bg-secondary);
  border: 1px solid var(--color-error-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-2);
}

.invalid-order-item .order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.validation-errors {
  font-size: var(--font-size-sm);
}

.error-item {
  margin-bottom: var(--spacing-1);
}

.error-field {
  font-weight: 600;
  color: var(--color-error);
}

.error-messages {
  color: var(--text-secondary);
  margin-left: var(--spacing-1);
}

.valid-orders {
  margin-top: var(--spacing-3);
}

.valid-order-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-1);
}

.valid-order-tag {
  background: var(--color-success-light);
  color: var(--color-success);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

/* 确认对话框样式 */
.confirm-dialog {
  text-align: center;
  padding: var(--spacing-3);
}

.confirm-dialog p {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

.confirm-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: center;
}

/* 结果显示样式 */
.success-message,
.error-message,
.mixed-results {
  text-align: center;
  padding: var(--spacing-3);
}

.success-icon,
.error-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-2);
}

/* 改进的结果显示 */
.result-header {
  text-align: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.result-summary {
  display: flex;
  gap: var(--spacing-2);
  justify-content: center;
  margin-top: var(--spacing-3);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  min-width: 80px;
}

.summary-item.success {
  background: var(--color-success-light);
  border: 1px solid var(--color-success);
}

.summary-item.failure {
  background: var(--color-error-light);
  border: 1px solid var(--color-error);
}

.summary-item.total {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.summary-icon {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-1);
}

.summary-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-1);
}

.summary-count {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.success-section,
.failure-section {
  text-align: left;
  margin-bottom: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
}







.success-section {
  background: var(--color-success-light);
  border: 1px solid var(--color-success);
}

.failure-section {
  background: var(--color-error-light);
  border: 1px solid var(--color-error);
}

.success-section h5,
.failure-section h5 {
  margin-bottom: var(--spacing-3);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.success-list,
.failure-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.success-item,
.failure-item {
  background: var(--bg-primary);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.success-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.failure-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.order-label {
  font-weight: 600;
   color: var(--text-primary);
}

.order-id {
  font-family: 'Courier New', monospace;
  background: var(--bg-secondary);
  padding: var(--spacing-1);
  border-radius: var(--radius-xs);
  font-size: var(--font-size-sm);
}

.failure-error {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.error-icon {
  font-size: var(--font-size-sm);
  margin-top: 2px;
}

.error-message {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.failure-suggestion {
  background: var(--bg-secondary);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  border-left: 3px solid var(--color-warning);
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
}

/* 多订单底部 */
.multi-order-footer {
  background: var(--bg-secondary);
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.batch-actions {
  display: flex;
  gap: var(--spacing-2);
}

.creation-summary {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.creation-summary span {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 选择框 */
.order-checkbox {
  position: absolute;
  top: var(--spacing-2);
  left: var(--spacing-2);
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* 动画 */
@keyframes multiOrderSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .multi-order-list {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .multi-order-list {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .multi-order-item-content {
    grid-template-columns: 1fr;
  }

  .multi-order-controls {
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-2);
  }

  .multi-order-footer {
    flex-direction: column;
    gap: var(--spacing-3);
  }
}

/* 子区域标题样式 */
.sub-section {
  margin-top: var(--spacing-4);
  margin-bottom: var(--spacing-2);
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-3);
}

.sub-section h4 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 独立特殊要求区域样式 */
.requirements-section-standalone {
  background: var(--bg-tertiary);
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--neu-shadow-outset);
  margin-bottom: var(--spacing-4);
  position: relative;
  transition: box-shadow var(--transition-normal);
  min-height: 120px;
  overflow: visible; /* 允许内容完全显示 */
}

.requirements-section-standalone:hover {
  box-shadow: var(--neu-shadow-hover);
}

/* 保留原有的特殊要求区域样式以兼容性 */
.requirements-section {
  background: var(--bg-tertiary);  /* 与田字格保持一致的背景 */
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--neu-shadow-outset);  /* 使用相同的Neumorphism阴影 */
  margin-bottom: var(--spacing-4);
  position: relative;  /* 为拖拽手柄做准备 */
  transition: box-shadow var(--transition-normal);  /* 与田字格一致的过渡效果 */
  resize: vertical;    /* 允许垂直调整大小 */
  min-height: 120px;   /* 设置最小高度 */
  overflow: visible;   /* 改为visible以防止内容被裁切 */
}

/* 特殊要求区域悬停效果 - 与田字格保持一致 */
.requirements-section:hover {
  box-shadow: var(--neu-shadow-hover);
}

.requirements-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
}

/* 整合到田字格中的特殊要求区域样式 */
.requirements-container-integrated {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3);
  margin-top: var(--spacing-2);
}

.requirements-container-integrated .requirements-left,
.requirements-container-integrated .requirements-right {
  min-height: auto;
}

.requirements-container-integrated .form-group {
  margin-bottom: var(--spacing-2);
}

.requirements-container-integrated textarea {
  min-height: 60px; /* 减少高度以适应田字格 */
  resize: vertical;
}

.requirements-left,
.requirements-right {
  display: flex;
  flex-direction: column;
}

.checkbox-group-vertical {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.checkbox-group-vertical .checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.checkbox-group-vertical .checkbox-label:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

