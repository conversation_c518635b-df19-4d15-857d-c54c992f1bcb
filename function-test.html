<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .iframe-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .test-iframe {
            width: 100%;
            height: 700px;
            border: none;
            display: block;
        }
        
        .test-instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-instructions h3 {
            margin: 0 0 15px 0;
            color: #1976d2;
        }
        
        .test-steps {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-steps h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .test-steps li {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        
        .success-criteria {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
        }
        
        .success-criteria h3 {
            margin: 0 0 15px 0;
            color: #2e7d32;
        }
        
        .success-criteria ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .success-criteria li {
            margin-bottom: 8px;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 OTA功能完整性测试</h1>
            <p>验证"额外要求"板块移动后的功能完整性</p>
        </div>
        
        <div class="test-instructions">
            <h3>📋 测试目标</h3>
            <p>确认"额外要求"板块从右列移动到左列后，所有功能保持正常工作，包括：</p>
            <ul>
                <li>板块在左列第4个位置正确显示</li>
                <li>textarea输入功能正常</li>
                <li>表单提交时数据正确传递</li>
                <li>布局在各屏幕尺寸下正常显示</li>
                <li>拖拽调整功能正常（如果适用）</li>
            </ul>
        </div>
        
        <div class="iframe-container">
            <iframe src="index.html" class="test-iframe"></iframe>
        </div>
        
        <div class="test-steps">
            <h3>🔍 测试步骤</h3>
            <ol>
                <li>观察左列布局：确认从上到下依次为 <span class="highlight">订单输入</span> → <span class="highlight">行程信息</span> → <span class="highlight">特殊需求</span> → <span class="highlight">额外要求</span></li>
                <li>观察右列布局：确认从上到下依次为 <span class="highlight">基本信息</span> → <span class="highlight">客户信息</span> → <span class="highlight">服务配置</span></li>
                <li>在"额外要求"的textarea中输入测试文本，确认输入功能正常</li>
                <li>尝试调整浏览器窗口大小，观察响应式布局是否正常</li>
                <li>检查底部操作按钮是否正常显示和跨列</li>
                <li>如果有拖拽手柄，测试板块高度调整功能</li>
            </ol>
        </div>
        
        <div class="success-criteria">
            <h3>✅ 成功标准</h3>
            <ul>
                <li><strong>布局正确</strong>：左列4个板块，右列3个板块，顺序正确</li>
                <li><strong>功能完整</strong>：额外要求textarea可以正常输入和编辑</li>
                <li><strong>视觉一致</strong>：板块样式与其他板块保持一致</li>
                <li><strong>响应式正常</strong>：在不同屏幕尺寸下布局适配正确</li>
                <li><strong>操作按钮可见</strong>：底部操作区域跨列显示，包含所有按钮</li>
                <li><strong>无功能缺失</strong>：移动后没有破坏任何现有功能</li>
            </ul>
        </div>
    </div>
</body>
</html>
