<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA 4+3布局测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .layout-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .layout-box {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }
        
        .layout-box h3 {
            margin: 0 0 15px 0;
            color: #495057;
            text-align: center;
        }
        
        .layout-structure {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-line;
            font-size: 11px;
            line-height: 1.3;
            border: 1px solid #dee2e6;
        }
        
        .old-layout {
            border-color: #dc3545;
        }
        
        .old-layout h3 {
            color: #dc3545;
        }
        
        .new-layout {
            border-color: #28a745;
        }
        
        .new-layout h3 {
            color: #28a745;
        }
        
        .test-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .test-btn:hover {
            background: #007bff;
            color: white;
        }
        
        .test-btn.active {
            background: #007bff;
            color: white;
        }
        
        .iframe-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.5s ease;
            margin-bottom: 20px;
        }
        
        .test-iframe {
            width: 100%;
            height: 800px;
            border: none;
            display: block;
        }
        
        .size-info {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #ddd;
        }
        
        .checklist {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .checklist h3 {
            margin: 0 0 10px 0;
            color: #2e7d32;
        }
        
        .checklist ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .checklist li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔄 OTA布局调整测试</h1>
            <p>验证"额外要求"板块从右列移动到左列的布局效果</p>
        </div>
        
        <div class="layout-comparison">
            <div class="layout-box old-layout">
                <h3>❌ 原始布局 (3+4)</h3>
                <div class="layout-structure">左列                    右列
┌─────────────┐    ┌─────────────┐
│  订单输入    │    │  基本信息    │
├─────────────┤    ├─────────────┤
│  行程信息    │    │  客户信息    │
├─────────────┤    ├─────────────┤
│  特殊需求    │    │  服务配置    │
└─────────────┘    ├─────────────┤
                   │  额外要求    │
                   └─────────────┘</div>
            </div>
            
            <div class="layout-box new-layout">
                <h3>✅ 新布局 (4+3)</h3>
                <div class="layout-structure">左列                    右列
┌─────────────┐    ┌─────────────┐
│  订单输入    │    │  基本信息    │
├─────────────┤    ├─────────────┤
│  行程信息    │    │  客户信息    │
├─────────────┤    ├─────────────┤
│  特殊需求    │    │  服务配置    │
├─────────────┤    └─────────────┘
│  额外要求    │
└─────────────┘</div>
            </div>
        </div>
        
        <div class="test-controls">
            <button class="test-btn active" onclick="setSize('desktop')">🖥️ 桌面端 (1200px)</button>
            <button class="test-btn" onclick="setSize('tablet')">📱 平板端 (900px)</button>
            <button class="test-btn" onclick="setSize('mobile')">📱 移动端 (375px)</button>
            <button class="test-btn" onclick="setSize('portrait')">📱 竖屏 (375×667)</button>
        </div>
        
        <div class="iframe-container" id="iframeContainer">
            <iframe src="index.html" class="test-iframe" id="testIframe"></iframe>
            <div class="size-info" id="sizeInfo">当前尺寸: 1200px × 800px (桌面端)</div>
        </div>
        
        <div class="checklist">
            <h3>✅ 验证要点</h3>
            <ul>
                <li><strong>左列4板块</strong>: 订单输入 → 行程信息 → 特殊需求 → 额外要求</li>
                <li><strong>右列3板块</strong>: 基本信息 → 客户信息 → 服务配置</li>
                <li><strong>额外要求功能</strong>: textarea正常工作，可以输入文本</li>
                <li><strong>操作按钮</strong>: 底部跨列显示，包含"数据异常提示"和"创建订单"</li>
                <li><strong>响应式适配</strong>: 各屏幕尺寸下布局正常，内容完全可见</li>
                <li><strong>拖拽调整</strong>: 板块高度可以正常调整</li>
            </ul>
        </div>
    </div>

    <script>
        function setSize(type) {
            const container = document.getElementById('iframeContainer');
            const iframe = document.getElementById('testIframe');
            const sizeInfo = document.getElementById('sizeInfo');
            const buttons = document.querySelectorAll('.test-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            let width, height, description;
            
            switch(type) {
                case 'desktop':
                    width = '1200px';
                    height = '800px';
                    description = '桌面端';
                    break;
                case 'tablet':
                    width = '900px';
                    height = '700px';
                    description = '平板端';
                    break;
                case 'mobile':
                    width = '375px';
                    height = '600px';
                    description = '移动端';
                    break;
                case 'portrait':
                    width = '375px';
                    height = '667px';
                    description = '竖屏';
                    break;
            }
            
            container.style.width = width;
            container.style.margin = '0 auto';
            iframe.style.height = height;
            sizeInfo.textContent = `当前尺寸: ${width} × ${height} (${description})`;
        }
        
        // 初始化
        setSize('desktop');
    </script>
</body>
</html>
